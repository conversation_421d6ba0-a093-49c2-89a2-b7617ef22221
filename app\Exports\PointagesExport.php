<?php

namespace App\Exports;

use App\Models\Pointage;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/**
 * Export Excel professionnel des pointages
 */
class PointagesExport implements 
    FromCollection, 
    WithHeadings, 
    WithMapping, 
    WithStyles, 
    WithColumnWidths,
    WithTitle
{
    protected Collection $pointages;

    public function __construct(Collection $pointages)
    {
        $this->pointages = $pointages;
    }

    /**
     * Collection des données à exporter
     */
    public function collection()
    {
        return $this->pointages;
    }

    /**
     * En-têtes des colonnes
     */
    public function headings(): array
    {
        return [
            'ID',
            'Employé',
            'Email',
            'Site',
            'Date',
            'Heure Début',
            'Heure Fin',
            'Durée',
            'Statut',
            'Latitude Début',
            'Longitude Début',
            'Latitude Fin',
            'Longitude Fin',
            'Créé le'
        ];
    }

    /**
     * Mapping des données pour chaque ligne
     */
    public function map($pointage): array
    {
        return [
            $pointage->id,
            $pointage->user->name ?? 'N/A',
            $pointage->user->email ?? 'N/A',
            $pointage->site->name ?? 'N/A',
            $pointage->debut_pointage ? $pointage->debut_pointage->format('Y-m-d') : 'N/A',
            $pointage->debut_pointage ? $pointage->debut_pointage->format('H:i:s') : 'N/A',
            $pointage->fin_pointage ? $pointage->fin_pointage->format('H:i:s') : 'En cours',
            $pointage->duree ?? ($pointage->fin_pointage ? 'Calculé' : 'En cours'),
            $pointage->fin_pointage ? 'Terminé' : 'En cours',
            $pointage->debut_latitude ?? 'N/A',
            $pointage->debut_longitude ?? 'N/A',
            $pointage->fin_latitude ?? 'N/A',
            $pointage->fin_longitude ?? 'N/A',
            $pointage->created_at ? $pointage->created_at->format('Y-m-d H:i:s') : 'N/A'
        ];
    }

    /**
     * Styles du fichier Excel
     */
    public function styles(Worksheet $sheet)
    {
        $lastRow = $this->pointages->count() + 1;
        $lastColumn = 'N';

        return [
            // Style de l'en-tête
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                    'size' => 12
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '2E86AB']
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER
                ]
            ],

            // Style des données
            "A2:{$lastColumn}{$lastRow}" => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'CCCCCC']
                    ]
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER
                ]
            ],

            // Style des lignes paires (zebra)
            "A2:{$lastColumn}{$lastRow}" => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F8F9FA']
                ]
            ]
        ];
    }

    /**
     * Largeurs des colonnes
     */
    public function columnWidths(): array
    {
        return [
            'A' => 8,   // ID
            'B' => 20,  // Employé
            'C' => 25,  // Email
            'D' => 25,  // Site
            'E' => 12,  // Date
            'F' => 12,  // Heure Début
            'G' => 12,  // Heure Fin
            'H' => 10,  // Durée
            'I' => 12,  // Statut
            'J' => 15,  // Latitude Début
            'K' => 15,  // Longitude Début
            'L' => 15,  // Latitude Fin
            'M' => 15,  // Longitude Fin
            'N' => 18   // Créé le
        ];
    }

    /**
     * Titre de la feuille
     */
    public function title(): string
    {
        return 'Pointages_' . now()->format('Y-m-d');
    }
}
