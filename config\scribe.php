<?php

return [
    'theme' => 'default',
    
    'title' => 'ClockIn API Documentation',
    
    'description' => 'Documentation complète de l\'API ClockIn pour la gestion du pointage des employés.',
    
    'base_url' => null,
    
    'routes' => [
        [
            'match' => [
                'domains' => ['*'],
                'prefixes' => ['api/*'],
                'versions' => ['v1'],
            ],
            'include' => [
                // Include all routes
            ],
            'exclude' => [
                // Exclude specific routes if needed
            ],
            'apply' => [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'response_calls' => [
                    'methods' => ['GET'],
                    'config' => [
                        'app.env' => 'documentation',
                    ],
                ],
            ],
        ],
    ],
    
    'type' => 'static',
    
    'static' => [
        'output_path' => 'public/docs',
    ],
    
    'laravel' => [
        'add_routes' => true,
        'docs_url' => '/docs',
        'assets_directory' => null,
        'middleware' => [],
    ],
    
    'try_it_out' => [
        'enabled' => true,
        'base_url' => null,
        'use_csrf' => false,
    ],
    
    'auth' => [
        'enabled' => true,
        'default' => false,
        'in' => 'bearer',
        'name' => 'Authorization',
        'use_value' => env('SCRIBE_AUTH_KEY'),
        'placeholder' => '{YOUR_AUTH_KEY}',
        'extra_info' => 'Vous pouvez récupérer votre token d\'authentification en vous connectant via l\'endpoint <code>POST /api/auth/login</code>.',
    ],
    
    'intro_text' => '
Cette documentation décrit l\'API ClockIn pour la gestion du pointage des employés.

## Authentification

L\'API utilise l\'authentification par token Bearer. Pour accéder aux endpoints protégés, vous devez :

1. Vous connecter via `POST /api/auth/login` avec vos identifiants
2. Utiliser le token retourné dans l\'en-tête `Authorization: Bearer {token}`

## Codes de réponse

- `200` - Succès
- `201` - Créé avec succès  
- `400` - Erreur de validation
- `401` - Non authentifié
- `403` - Accès refusé
- `404` - Ressource non trouvée
- `500` - Erreur serveur

## Langues

Les messages d\'erreur sont disponibles en français et en arabe.
',
    
    'example_languages' => [
        'bash',
        'javascript',
        'php',
        'python',
    ],
    
    'fractal' => [
        'serializer' => null,
    ],
    
    'faker_seed' => null,
    
    'strategies' => [
        'metadata' => [
            \Knuckles\Scribe\Extracting\Strategies\Metadata\GetFromDocBlocks::class,
        ],
        'urlParameters' => [
            // \Knuckles\Scribe\Extracting\Strategies\UrlParameters\GetFromLaravelAPI::class,
            // \Knuckles\Scribe\Extracting\Strategies\UrlParameters\GetFromDocBlocks::class,
        ],
        'queryParameters' => [
            // \Knuckles\Scribe\Extracting\Strategies\QueryParameters\GetFromFormRequest::class,
            // \Knuckles\Scribe\Extracting\Strategies\QueryParameters\GetFromInlineValidator::class,
            // \Knuckles\Scribe\Extracting\Strategies\QueryParameters\GetFromDocBlocks::class,
        ],
        'headers' => [
            \Knuckles\Scribe\Extracting\Strategies\Headers\GetFromRouteRules::class,
            \Knuckles\Scribe\Extracting\Strategies\Headers\GetFromDocBlocks::class,
        ],
        'bodyParameters' => [
            \Knuckles\Scribe\Extracting\Strategies\BodyParameters\GetFromFormRequest::class,
            \Knuckles\Scribe\Extracting\Strategies\BodyParameters\GetFromInlineValidator::class,
            \Knuckles\Scribe\Extracting\Strategies\BodyParameters\GetFromDocBlocks::class,
        ],
        'responses' => [
            \Knuckles\Scribe\Extracting\Strategies\Responses\UseTransformerTags::class,
            \Knuckles\Scribe\Extracting\Strategies\Responses\UseApiResourceTags::class,
            \Knuckles\Scribe\Extracting\Strategies\Responses\UseResponseTag::class,
            \Knuckles\Scribe\Extracting\Strategies\Responses\UseResponseFileTag::class,
            \Knuckles\Scribe\Extracting\Strategies\Responses\ResponseCalls::class,
        ],
        'responseFields' => [
            \Knuckles\Scribe\Extracting\Strategies\ResponseFields\GetFromResponseFieldTag::class,
        ],
    ],
    
    'database_connections_to_transact' => [config('database.default')],
];
