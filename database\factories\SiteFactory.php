<?php

namespace Database\Factories;

use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Factory pour le modèle Site
 */
class SiteFactory extends Factory
{
    protected $model = Site::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        // Coordonnées aléatoires dans la région du Maroc
        $latitude = $this->faker->randomFloat(6, 27.0, 36.0);
        $longitude = $this->faker->randomFloat(6, -13.0, -1.0);

        return [
            'name' => $this->faker->company() . ' - ' . $this->faker->city(),
            'latitude' => $latitude,
            'longitude' => $longitude,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Site à Casablanca
     */
    public function casablanca(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Site Casablanca',
            'latitude' => 33.5731,
            'longitude' => -7.5898,
        ]);
    }

    /**
     * Site à Rabat
     */
    public function rabat(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Site Rabat',
            'latitude' => 34.0209,
            'longitude' => -6.8416,
        ]);
    }

    /**
     * Site à Marrakech
     */
    public function marrakech(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Site Marrakech',
            'latitude' => 31.6295,
            'longitude' => -7.9811,
        ]);
    }

    /**
     * Site avec coordonnées précises
     */
    public function withCoordinates(float $latitude, float $longitude): static
    {
        return $this->state(fn (array $attributes) => [
            'latitude' => $latitude,
            'longitude' => $longitude,
        ]);
    }
}
