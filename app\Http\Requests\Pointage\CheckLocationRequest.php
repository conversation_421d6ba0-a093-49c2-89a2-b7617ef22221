<?php

namespace App\Http\Requests\Pointage;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Validation robuste pour la vérification de localisation
 */
class CheckLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'latitude' => [
                'required',
                'numeric',
                'between:-90,90',
                'regex:/^-?([1-8]?[0-9](\.[0-9]{1,8})?|90(\.0{1,8})?)$/',
            ],
            'longitude' => [
                'required',
                'numeric',
                'between:-180,180',
                'regex:/^-?((1[0-7][0-9]|[1-9]?[0-9])(\.[0-9]{1,8})?|180(\.0{1,8})?)$/',
            ],
            'accuracy' => [
                'sometimes',
                'numeric',
                'min:0',
                'max:1000', // Précision maximale en mètres
            ],
            'timestamp' => [
                'sometimes',
                'integer',
                'min:' . (now()->subHours(24)->timestamp), // Pas plus de 24h dans le passé
                'max:' . (now()->addMinutes(5)->timestamp), // Pas plus de 5 min dans le futur
            ],
            'altitude' => [
                'sometimes',
                'numeric',
                'between:-500,9000', // Entre 500m sous le niveau de la mer et 9000m d'altitude
            ],
            'speed' => [
                'sometimes',
                'numeric',
                'min:0',
                'max:200', // Vitesse maximale raisonnable en km/h
            ],
            'heading' => [
                'sometimes',
                'numeric',
                'between:0,360',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'latitude.required' => 'La latitude est requise.',
            'latitude.numeric' => 'La latitude doit être un nombre.',
            'latitude.between' => 'La latitude doit être comprise entre -90 et 90 degrés.',
            'latitude.regex' => 'Format de latitude invalide.',
            
            'longitude.required' => 'La longitude est requise.',
            'longitude.numeric' => 'La longitude doit être un nombre.',
            'longitude.between' => 'La longitude doit être comprise entre -180 et 180 degrés.',
            'longitude.regex' => 'Format de longitude invalide.',
            
            'accuracy.numeric' => 'La précision doit être un nombre.',
            'accuracy.min' => 'La précision ne peut pas être négative.',
            'accuracy.max' => 'La précision ne peut pas dépasser 1000 mètres.',
            
            'timestamp.integer' => 'Le timestamp doit être un entier.',
            'timestamp.min' => 'Le timestamp ne peut pas être antérieur à 24 heures.',
            'timestamp.max' => 'Le timestamp ne peut pas être dans le futur.',
            
            'altitude.numeric' => 'L\'altitude doit être un nombre.',
            'altitude.between' => 'L\'altitude doit être comprise entre -500 et 9000 mètres.',
            
            'speed.numeric' => 'La vitesse doit être un nombre.',
            'speed.min' => 'La vitesse ne peut pas être négative.',
            'speed.max' => 'La vitesse ne peut pas dépasser 200 km/h.',
            
            'heading.numeric' => 'La direction doit être un nombre.',
            'heading.between' => 'La direction doit être comprise entre 0 et 360 degrés.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'latitude' => 'latitude',
            'longitude' => 'longitude',
            'accuracy' => 'précision',
            'timestamp' => 'horodatage',
            'altitude' => 'altitude',
            'speed' => 'vitesse',
            'heading' => 'direction',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validation personnalisée pour vérifier la cohérence des coordonnées
            $this->validateCoordinatesConsistency($validator);
            
            // Validation de la précision GPS
            $this->validateGpsAccuracy($validator);
            
            // Validation anti-spoofing basique
            $this->validateAntiSpoofing($validator);
        });
    }

    /**
     * Valide la cohérence des coordonnées
     */
    private function validateCoordinatesConsistency($validator): void
    {
        $latitude = $this->input('latitude');
        $longitude = $this->input('longitude');

        if ($latitude && $longitude) {
            // Vérifier que les coordonnées ne sont pas exactement 0,0 (souvent un bug GPS)
            if ($latitude == 0 && $longitude == 0) {
                $validator->errors()->add('coordinates', 
                    'Les coordonnées 0,0 ne sont pas autorisées (erreur GPS probable).'
                );
            }

            // Vérifier que les coordonnées sont dans une zone géographique raisonnable
            // (par exemple, pour le Maroc)
            if (!$this->isWithinReasonableGeographicBounds($latitude, $longitude)) {
                $validator->errors()->add('coordinates', 
                    'Les coordonnées sont en dehors de la zone géographique autorisée.'
                );
            }
        }
    }

    /**
     * Valide la précision GPS
     */
    private function validateGpsAccuracy($validator): void
    {
        $accuracy = $this->input('accuracy');

        if ($accuracy !== null) {
            // Si la précision est trop faible (> 100m), avertir
            if ($accuracy > 100) {
                $validator->errors()->add('accuracy', 
                    'La précision GPS est insuffisante (> 100m). Veuillez vous rapprocher d\'une zone avec un meilleur signal.'
                );
            }
        }
    }

    /**
     * Validation anti-spoofing basique
     */
    private function validateAntiSpoofing($validator): void
    {
        $latitude = $this->input('latitude');
        $longitude = $this->input('longitude');
        $speed = $this->input('speed');
        $timestamp = $this->input('timestamp');

        // Vérifier la vitesse de déplacement si on a un timestamp
        if ($timestamp && $speed !== null && $speed > 150) {
            $validator->errors()->add('speed', 
                'Vitesse de déplacement suspecte détectée.'
            );
        }

        // Vérifier les coordonnées trop précises (possiblement falsifiées)
        if ($latitude && $longitude) {
            $latDecimals = strlen(substr(strrchr($latitude, "."), 1));
            $lonDecimals = strlen(substr(strrchr($longitude, "."), 1));
            
            if ($latDecimals > 8 || $lonDecimals > 8) {
                $validator->errors()->add('coordinates', 
                    'Précision des coordonnées suspecte.'
                );
            }
        }
    }

    /**
     * Vérifie si les coordonnées sont dans une zone géographique raisonnable
     */
    private function isWithinReasonableGeographicBounds(float $latitude, float $longitude): bool
    {
        // Exemple pour le Maroc et pays limitrophes
        // Vous pouvez ajuster ces limites selon votre zone d'opération
        $bounds = [
            'north' => 36.0,   // Nord du Maroc
            'south' => 20.0,   // Sud du Maroc
            'west' => -18.0,   // Ouest du Maroc
            'east' => 0.0,     // Est du Maroc
        ];

        return $latitude >= $bounds['south'] && 
               $latitude <= $bounds['north'] && 
               $longitude >= $bounds['west'] && 
               $longitude <= $bounds['east'];
    }

    /**
     * Prépare les données pour la validation
     */
    protected function prepareForValidation(): void
    {
        // Nettoyer et normaliser les coordonnées
        if ($this->has('latitude')) {
            $this->merge([
                'latitude' => $this->sanitizeCoordinate($this->input('latitude'))
            ]);
        }

        if ($this->has('longitude')) {
            $this->merge([
                'longitude' => $this->sanitizeCoordinate($this->input('longitude'))
            ]);
        }

        // Ajouter un timestamp si manquant
        if (!$this->has('timestamp')) {
            $this->merge([
                'timestamp' => now()->timestamp
            ]);
        }
    }

    /**
     * Nettoie et normalise une coordonnée
     */
    private function sanitizeCoordinate($coordinate)
    {
        // Supprimer les espaces
        $coordinate = trim($coordinate);
        
        // Convertir en float et limiter la précision
        if (is_numeric($coordinate)) {
            return round((float) $coordinate, 8);
        }
        
        return $coordinate;
    }
}
