-- Script de vérification de la structure de la base de données ClockIn
-- Ce script vérifie que la structure correspond exactement à la structure originale

-- Vérification de la table users
DESCRIBE users;

-- Vérification de la table sites
DESCRIBE sites;

-- Vérification de la table pointages
DESCRIBE pointages;

-- Vérification de la table verifications
DESCRIBE verifications;

-- Vérification de la table assignments
DESCRIBE assignments;

-- Vérification de la table logs
DESCRIBE logs;

-- Vérification des index
SHOW INDEX FROM users;
SHOW INDEX FROM sites;
SHOW INDEX FROM pointages;
SHOW INDEX FROM verifications;
SHOW INDEX FROM assignments;
SHOW INDEX FROM logs;

-- Vérification des contraintes de clés étrangères
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
    REFERENCED_TABLE_SCHEMA = 'clockin_db' 
    AND TABLE_SCHEMA = 'clockin_db'
ORDER BY TABLE_NAME, COLUMN_NAME;
