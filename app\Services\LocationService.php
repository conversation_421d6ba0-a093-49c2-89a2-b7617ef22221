<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * Service de géolocalisation pour calculer les distances
 * et vérifier la proximité des utilisateurs avec les sites
 */
class LocationService
{
    /**
     * Rayon de la Terre en mètres
     */
    private const EARTH_RADIUS = 6371000;

    /**
     * Distance maximale par défaut en mètres
     */
    private const DEFAULT_MAX_DISTANCE = 50;

    /**
     * Calcule la distance entre deux points géographiques
     * en utilisant la formule de Haversine
     *
     * @param float $lat1 Latitude du premier point
     * @param float $lon1 Longitude du premier point
     * @param float $lat2 Latitude du second point
     * @param float $lon2 Longitude du second point
     * @return float Distance en mètres
     */
    public function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        try {
            // Validation des coordonnées
            $this->validateCoordinates($lat1, $lon1);
            $this->validateCoordinates($lat2, $lon2);

            // Conversion en radians
            $latFrom = deg2rad($lat1);
            $lonFrom = deg2rad($lon1);
            $latTo = deg2rad($lat2);
            $lonTo = deg2rad($lon2);

            // Calcul des différences
            $latDelta = $latTo - $latFrom;
            $lonDelta = $lonTo - $lonFrom;

            // Formule de Haversine
            $a = sin($latDelta / 2) * sin($latDelta / 2) +
                 cos($latFrom) * cos($latTo) *
                 sin($lonDelta / 2) * sin($lonDelta / 2);
            
            $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
            
            $distance = self::EARTH_RADIUS * $c;

            Log::debug('Distance calculée', [
                'from' => [$lat1, $lon1],
                'to' => [$lat2, $lon2],
                'distance' => $distance
            ]);

            return $distance;

        } catch (\Exception $e) {
            Log::error('Erreur lors du calcul de distance', [
                'coordinates' => [$lat1, $lon1, $lat2, $lon2],
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Vérifie si un point est dans le rayon autorisé d'un autre point
     *
     * @param float $lat1 Latitude du point de référence
     * @param float $lon1 Longitude du point de référence
     * @param float $lat2 Latitude du point à vérifier
     * @param float $lon2 Longitude du point à vérifier
     * @param float $maxDistance Distance maximale autorisée en mètres
     * @return bool True si dans le rayon, false sinon
     */
    public function isWithinRange(
        float $lat1, 
        float $lon1, 
        float $lat2, 
        float $lon2, 
        float $maxDistance = self::DEFAULT_MAX_DISTANCE
    ): bool {
        $distance = $this->calculateDistance($lat1, $lon1, $lat2, $lon2);
        return $distance <= $maxDistance;
    }

    /**
     * Calcule la distance et retourne les informations complètes
     *
     * @param float $lat1 Latitude du premier point
     * @param float $lon1 Longitude du premier point
     * @param float $lat2 Latitude du second point
     * @param float $lon2 Longitude du second point
     * @param float $maxDistance Distance maximale autorisée
     * @return array Informations sur la distance et la proximité
     */
    public function getDistanceInfo(
        float $lat1, 
        float $lon1, 
        float $lat2, 
        float $lon2, 
        float $maxDistance = self::DEFAULT_MAX_DISTANCE
    ): array {
        $distance = $this->calculateDistance($lat1, $lon1, $lat2, $lon2);
        $isWithinRange = $distance <= $maxDistance;

        return [
            'distance' => round($distance, 2),
            'within_range' => $isWithinRange,
            'max_distance' => $maxDistance,
            'coordinates' => [
                'from' => ['lat' => $lat1, 'lon' => $lon1],
                'to' => ['lat' => $lat2, 'lon' => $lon2]
            ]
        ];
    }

    /**
     * Valide les coordonnées géographiques
     *
     * @param float $latitude
     * @param float $longitude
     * @throws \InvalidArgumentException
     */
    private function validateCoordinates(float $latitude, float $longitude): void
    {
        if ($latitude < -90 || $latitude > 90) {
            throw new \InvalidArgumentException("Latitude invalide: {$latitude}. Doit être entre -90 et 90.");
        }

        if ($longitude < -180 || $longitude > 180) {
            throw new \InvalidArgumentException("Longitude invalide: {$longitude}. Doit être entre -180 et 180.");
        }
    }

    /**
     * Convertit une distance en différentes unités
     *
     * @param float $distanceInMeters Distance en mètres
     * @return array Distance dans différentes unités
     */
    public function convertDistance(float $distanceInMeters): array
    {
        return [
            'meters' => round($distanceInMeters, 2),
            'kilometers' => round($distanceInMeters / 1000, 3),
            'feet' => round($distanceInMeters * 3.28084, 2),
            'miles' => round($distanceInMeters / 1609.344, 3)
        ];
    }

    /**
     * Calcule le point central entre plusieurs coordonnées
     *
     * @param array $coordinates Tableau de coordonnées [['lat' => x, 'lon' => y], ...]
     * @return array Point central ['lat' => x, 'lon' => y]
     */
    public function calculateCenterPoint(array $coordinates): array
    {
        if (empty($coordinates)) {
            throw new \InvalidArgumentException('Au moins une coordonnée est requise');
        }

        $count = count($coordinates);
        $totalLat = 0;
        $totalLon = 0;

        foreach ($coordinates as $coord) {
            $this->validateCoordinates($coord['lat'], $coord['lon']);
            $totalLat += $coord['lat'];
            $totalLon += $coord['lon'];
        }

        return [
            'lat' => $totalLat / $count,
            'lon' => $totalLon / $count
        ];
    }

    /**
     * Génère un périmètre circulaire autour d'un point
     *
     * @param float $centerLat Latitude du centre
     * @param float $centerLon Longitude du centre
     * @param float $radius Rayon en mètres
     * @param int $points Nombre de points pour le périmètre
     * @return array Points du périmètre
     */
    public function generateCirclePerimeter(
        float $centerLat, 
        float $centerLon, 
        float $radius, 
        int $points = 16
    ): array {
        $this->validateCoordinates($centerLat, $centerLon);
        
        $perimeter = [];
        $angleStep = 360 / $points;

        for ($i = 0; $i < $points; $i++) {
            $angle = deg2rad($i * $angleStep);
            
            // Calcul approximatif pour de petites distances
            $latOffset = ($radius / self::EARTH_RADIUS) * cos($angle) * (180 / M_PI);
            $lonOffset = ($radius / self::EARTH_RADIUS) * sin($angle) * (180 / M_PI) / cos(deg2rad($centerLat));
            
            $perimeter[] = [
                'lat' => $centerLat + $latOffset,
                'lon' => $centerLon + $lonOffset
            ];
        }

        return $perimeter;
    }
}
