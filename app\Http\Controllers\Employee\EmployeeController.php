<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

/**
 * @group Employee Management
 *
 * APIs pour la gestion des employés (Admin uniquement)
 */
class EmployeeController extends Controller
{
    /**
     * List Employees
     *
     * Récupère la liste de tous les employés
     *
     * @authenticated
     *
     * @queryParam page integer Numéro de page pour la pagination. Example: 1
     * @queryParam per_page integer Nombre d'éléments par page (max 50). Example: 15
     * @queryParam search string Recherche par nom ou email. Example: Ahmed
     * @queryParam role string Filtrer par rôle (admin, employee). Example: employee
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "data": [
     *       {
     *         "id": 2,
     *         "name": "<PERSON>",
     *         "email": "<EMAIL>",
     *         "role": "employee",
     *         "created_at": "2025-06-05 08:00:00"
     *       }
     *     ],
     *     "current_page": 1,
     *     "total": 3,
     *     "per_page": 15
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 50);
        $search = $request->get('search');
        $role = $request->get('role');

        $query = User::query();

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($role && in_array($role, ['admin', 'employee'])) {
            $query->where('role', $role);
        }

        $users = $query->orderBy('created_at', 'desc')->paginate($perPage);

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'employees_list',
            'details' => 'Consultation de la liste des employés'
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'data' => UserResource::collection($users->items()),
                'current_page' => $users->currentPage(),
                'total' => $users->total(),
                'per_page' => $users->perPage(),
                'last_page' => $users->lastPage()
            ]
        ]);
    }

    /**
     * Create Employee
     *
     * Crée un nouvel employé
     *
     * @authenticated
     *
     * @bodyParam name string required Le nom de l'employé. Example: Nouveau Employé
     * @bodyParam email string required L'email de l'employé. Example: <EMAIL>
     * @bodyParam password string required Le mot de passe (min 6 caractères). Example: password123
     * @bodyParam role string required Le rôle (admin ou employee). Example: employee
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Employé créé avec succès",
     *   "message_ar": "تم إنشاء الموظف بنجاح",
     *   "data": {
     *     "id": 5,
     *     "name": "Nouveau Employé",
     *     "email": "<EMAIL>",
     *     "role": "employee",
     *     "created_at": "2025-06-05 08:00:00"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6',
            'role' => 'required|in:admin,employee',
        ], [
            'name.required' => 'Le nom est requis. / الاسم مطلوب.',
            'email.required' => 'L\'email est requis. / البريد الإلكتروني مطلوب.',
            'email.email' => 'L\'email doit être valide. / يجب أن يكون البريد الإلكتروني صالحًا.',
            'email.unique' => 'Cet email existe déjà. / هذا البريد الإلكتروني موجود بالفعل.',
            'password.required' => 'Le mot de passe est requis. / كلمة المرور مطلوبة.',
            'password.min' => 'Le mot de passe doit contenir au moins 6 caractères. / يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل.',
            'role.required' => 'Le rôle est requis. / الدور مطلوب.',
            'role.in' => 'Le rôle doit être admin ou employee. / يجب أن يكون الدور admin أو employee.',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
        ]);

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'employee_created',
            'details' => "Création de l'employé: {$user->name} ({$user->email})"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Employé créé avec succès.',
            'message_ar' => 'تم إنشاء الموظف بنجاح.',
            'data' => new UserResource($user)
        ], 201);
    }

    /**
     * Show Employee
     *
     * Affiche les détails d'un employé spécifique
     *
     * @authenticated
     *
     * @urlParam user integer required L'ID de l'employé. Example: 2
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "id": 2,
     *     "name": "Ahmed Benali",
     *     "email": "<EMAIL>",
     *     "role": "employee",
     *     "created_at": "2025-06-05 08:00:00"
     *   }
     * }
     */
    public function show(User $user): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => new UserResource($user)
        ]);
    }

    /**
     * Update Employee
     *
     * Met à jour un employé existant
     *
     * @authenticated
     *
     * @urlParam user integer required L'ID de l'employé. Example: 2
     * @bodyParam name string required Le nom de l'employé. Example: Ahmed Benali Modifié
     * @bodyParam email string required L'email de l'employé. Example: <EMAIL>
     * @bodyParam password string Le nouveau mot de passe (optionnel). Example: newpassword123
     * @bodyParam role string required Le rôle (admin ou employee). Example: employee
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Employé mis à jour avec succès",
     *   "message_ar": "تم تحديث الموظف بنجاح",
     *   "data": {
     *     "id": 2,
     *     "name": "Ahmed Benali Modifié",
     *     "email": "<EMAIL>",
     *     "role": "employee",
     *     "updated_at": "2025-06-05 08:30:00"
     *   }
     * }
     */
    public function update(Request $request, User $user): JsonResponse
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'role' => 'required|in:admin,employee',
        ];

        if ($request->filled('password')) {
            $rules['password'] = 'string|min:6';
        }

        $request->validate($rules, [
            'name.required' => 'Le nom est requis. / الاسم مطلوب.',
            'email.required' => 'L\'email est requis. / البريد الإلكتروني مطلوب.',
            'email.email' => 'L\'email doit être valide. / يجب أن يكون البريد الإلكتروني صالحًا.',
            'email.unique' => 'Cet email existe déjà. / هذا البريد الإلكتروني موجود بالفعل.',
            'password.min' => 'Le mot de passe doit contenir au moins 6 caractères. / يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل.',
            'role.required' => 'Le rôle est requis. / الدور مطلوب.',
            'role.in' => 'Le rôle doit être admin ou employee. / يجب أن يكون الدور admin أو employee.',
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $oldData = $user->only(['name', 'email', 'role']);
        $user->update($updateData);

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'employee_updated',
            'details' => "Modification de l'employé: {$oldData['name']} -> {$user->name}"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Employé mis à jour avec succès.',
            'message_ar' => 'تم تحديث الموظف بنجاح.',
            'data' => new UserResource($user)
        ]);
    }

    /**
     * Delete Employee
     *
     * Supprime un employé (seulement si aucun pointage n'y est associé)
     *
     * @authenticated
     *
     * @urlParam user integer required L'ID de l'employé. Example: 2
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Employé supprimé avec succès",
     *   "message_ar": "تم حذف الموظف بنجاح"
     * }
     *
     * @response 400 {
     *   "success": false,
     *   "message": "Impossible de supprimer cet employé car il a des pointages",
     *   "message_ar": "لا يمكن حذف هذا الموظف لأن لديه تسجيلات حضور"
     * }
     */
    public function destroy(Request $request, User $user): JsonResponse
    {
        // Empêcher la suppression de son propre compte
        if ($user->id === $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Vous ne pouvez pas supprimer votre propre compte.',
                'message_ar' => 'لا يمكنك حذف حسابك الخاص.'
            ], 400);
        }

        // Vérifier s'il y a des pointages associés
        if ($user->pointages()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer cet employé car il a des pointages.',
                'message_ar' => 'لا يمكن حذف هذا الموظف لأن لديه تسجيلات حضور.'
            ], 400);
        }

        $userName = $user->name;
        $user->delete();

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'employee_deleted',
            'details' => "Suppression de l'employé: {$userName}"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Employé supprimé avec succès.',
            'message_ar' => 'تم حذف الموظف بنجاح.'
        ]);
    }
}
