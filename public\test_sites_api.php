<?php

// Test direct de l'API Sites sans passer par Lara<PERSON>
header('Content-Type: application/json');

try {
    // Connexion à la base de données
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=clockin_db', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Récupération des sites
    $stmt = $pdo->query("
        SELECT 
            s.id,
            s.name,
            s.latitude,
            s.longitude,
            s.created_at,
            COUNT(p.id) as pointages_count
        FROM sites s
        LEFT JOIN pointages p ON s.id = p.site_id
        GROUP BY s.id, s.name, s.latitude, s.longitude, s.created_at
        ORDER BY s.created_at DESC
    ");
    
    $sites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format de réponse similaire à Laravel
    $response = [
        'success' => true,
        'data' => [
            'data' => $sites,
            'total' => count($sites)
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (PDOException $e) {
    $response = [
        'success' => false,
        'message' => 'Erreur de base de données: ' . $e->getMessage()
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT);
}
