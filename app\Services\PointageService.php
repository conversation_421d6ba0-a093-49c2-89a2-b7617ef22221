<?php

namespace App\Services;

use App\Models\Pointage;
use App\Models\Site;
use App\Models\User;
use App\Models\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log as LaravelLog;
use Illuminate\Support\Facades\Cache;

/**
 * Service métier pour la gestion des pointages
 * Centralise la logique métier et améliore la testabilité
 */
class PointageService
{
    public function __construct(
        private LocationService $locationService
    ) {}

    /**
     * Traite une demande de pointage (début ou fin)
     */
    public function processPointage(User $user, Site $site, float $latitude, float $longitude): array
    {
        // Chercher un pointage actif pour cet utilisateur
        $activePointage = $this->getActivePointage($user->id);

        if ($activePointage) {
            return $this->endPointage($activePointage, $latitude, $longitude);
        }

        return $this->startPointage($user, $site, $latitude, $longitude);
    }

    /**
     * Démarre un nouveau pointage
     */
    public function startPointage(User $user, Site $site, float $latitude, float $longitude): array
    {
        $pointage = Pointage::create([
            'user_id' => $user->id,
            'site_id' => $site->id,
            'debut_pointage' => Carbon::now(),
            'debut_latitude' => $latitude,
            'debut_longitude' => $longitude,
        ]);

        // Invalider le cache des pointages actifs
        $this->invalidateUserCache($user->id);

        // Log l'événement
        LaravelLog::channel('pointage')->info('Début de pointage', [
            'pointage_id' => $pointage->id,
            'user_id' => $user->id,
            'site_id' => $site->id,
            'coordinates' => [$latitude, $longitude]
        ]);

        // Log métier
        Log::create([
            'user_id' => $user->id,
            'action' => 'pointage_start',
            'details' => json_encode([
                'pointage_id' => $pointage->id,
                'site_name' => $site->name,
                'coordinates' => [$latitude, $longitude]
            ])
        ]);

        return [
            'type' => 'start',
            'pointage' => $pointage,
            'message' => 'Début du pointage enregistré.',
            'message_ar' => 'تم تسجيل بداية الحضور.'
        ];
    }

    /**
     * Termine un pointage existant
     */
    public function endPointage(Pointage $pointage, float $latitude, float $longitude): array
    {
        $now = Carbon::now();
        $duration = $this->calculateDuration($pointage->debut_pointage, $now);

        $pointage->update([
            'fin_pointage' => $now,
            'fin_latitude' => $latitude,
            'fin_longitude' => $longitude,
            'duree' => $duration
        ]);

        // Invalider le cache
        $this->invalidateUserCache($pointage->user_id);

        // Log l'événement
        LaravelLog::channel('pointage')->info('Fin de pointage', [
            'pointage_id' => $pointage->id,
            'user_id' => $pointage->user_id,
            'duration' => $duration,
            'coordinates' => [$latitude, $longitude]
        ]);

        // Log métier
        Log::create([
            'user_id' => $pointage->user_id,
            'action' => 'pointage_end',
            'details' => json_encode([
                'pointage_id' => $pointage->id,
                'duration' => $duration,
                'coordinates' => [$latitude, $longitude]
            ])
        ]);

        return [
            'type' => 'end',
            'pointage' => $pointage->fresh(),
            'message' => 'Fin du pointage enregistrée.',
            'message_ar' => 'تم تسجيل نهاية الحضور.'
        ];
    }

    /**
     * Récupère le pointage actif d'un utilisateur
     */
    public function getActivePointage(int $userId): ?Pointage
    {
        return Cache::remember("active_pointage:{$userId}", 300, function () use ($userId) {
            return Pointage::where('user_id', $userId)
                ->whereNull('fin_pointage')
                ->with(['site'])
                ->first();
        });
    }

    /**
     * Vérifie si un utilisateur peut pointer sur un site
     */
    public function canUserPointAtSite(User $user, Site $site, float $latitude, float $longitude): array
    {
        $distanceInfo = $this->locationService->getDistanceInfo(
            (float) $site->latitude,
            (float) $site->longitude,
            $latitude,
            $longitude,
            config('clockin.max_distance', 50)
        );

        return [
            'can_point' => $distanceInfo['within_range'],
            'distance_info' => $distanceInfo,
            'site' => $site
        ];
    }

    /**
     * Calcule la durée entre deux timestamps
     */
    public function calculateDuration(Carbon $start, Carbon $end): string
    {
        if ($end->lessThan($start)) {
            return '00:00:00';
        }

        $diff = $end->diff($start);
        $totalHours = $diff->days * 24 + $diff->h;
        
        return sprintf('%02d:%02d:%02d', $totalHours, $diff->i, $diff->s);
    }

    /**
     * Récupère les statistiques de pointage pour un utilisateur
     */
    public function getUserPointageStats(int $userId, Carbon $from = null, Carbon $to = null): array
    {
        $from = $from ?? Carbon::now()->startOfMonth();
        $to = $to ?? Carbon::now()->endOfMonth();

        $cacheKey = "pointage_stats:{$userId}:" . $from->format('Y-m-d') . ':' . $to->format('Y-m-d');

        return Cache::remember($cacheKey, 3600, function () use ($userId, $from, $to) {
            $pointages = Pointage::where('user_id', $userId)
                ->whereBetween('debut_pointage', [$from, $to])
                ->whereNotNull('fin_pointage')
                ->get();

            $totalHours = 0;
            $totalDays = $pointages->count();

            foreach ($pointages as $pointage) {
                if ($pointage->duree) {
                    $parts = explode(':', $pointage->duree);
                    $totalHours += (int)$parts[0] + ((int)$parts[1] / 60) + ((int)$parts[2] / 3600);
                }
            }

            return [
                'total_days' => $totalDays,
                'total_hours' => round($totalHours, 2),
                'average_hours_per_day' => $totalDays > 0 ? round($totalHours / $totalDays, 2) : 0,
                'period' => [
                    'from' => $from->format('Y-m-d'),
                    'to' => $to->format('Y-m-d')
                ]
            ];
        });
    }

    /**
     * Invalide le cache lié à un utilisateur
     */
    private function invalidateUserCache(int $userId): void
    {
        Cache::forget("active_pointage:{$userId}");
        Cache::forget("user_site_assignment:{$userId}");
        
        // Invalider les stats du mois en cours
        $currentMonth = Carbon::now()->format('Y-m');
        Cache::forget("pointage_stats:{$userId}:{$currentMonth}-01:{$currentMonth}-31");
    }

    /**
     * Vérifie les pointages oubliés (non terminés depuis plus de 12h)
     */
    public function checkForgottenPointages(): array
    {
        $threshold = Carbon::now()->subHours(12);
        
        $forgottenPointages = Pointage::whereNull('fin_pointage')
            ->where('debut_pointage', '<', $threshold)
            ->with(['user', 'site'])
            ->get();

        $results = [];
        foreach ($forgottenPointages as $pointage) {
            LaravelLog::channel('pointage')->warning('Pointage oublié détecté', [
                'pointage_id' => $pointage->id,
                'user_id' => $pointage->user_id,
                'user_name' => $pointage->user->name,
                'site_name' => $pointage->site->name,
                'debut_pointage' => $pointage->debut_pointage->toISOString()
            ]);

            $results[] = [
                'pointage_id' => $pointage->id,
                'user' => $pointage->user->name,
                'site' => $pointage->site->name,
                'started_at' => $pointage->debut_pointage,
                'hours_ago' => $pointage->debut_pointage->diffInHours(Carbon::now())
            ];
        }

        return $results;
    }
}
