<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Pointage extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'site_id',
        'debut_pointage',
        'fin_pointage',
        'duree',
        'debut_latitude',
        'debut_longitude',
        'fin_latitude',
        'fin_longitude',
    ];

    protected $casts = [
        'debut_pointage' => 'datetime',
        'fin_pointage' => 'datetime',
        'debut_latitude' => 'decimal:8',
        'debut_longitude' => 'decimal:8',
        'fin_latitude' => 'decimal:8',
        'fin_longitude' => 'decimal:8',
    ];

    /**
     * Get the user that owns the pointage.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the site that owns the pointage.
     */
    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Calculate duration automatically when fin_pointage is set.
     */
    public function calculateDuration(): void
    {
        if ($this->debut_pointage && $this->fin_pointage) {
            $debut = Carbon::parse($this->debut_pointage);
            $fin = Carbon::parse($this->fin_pointage);
            $this->duree = $debut->diff($fin)->format('%H:%I:%S');
        }
    }

    /**
     * Check if pointage is active (not finished).
     */
    public function isActive(): bool
    {
        return is_null($this->fin_pointage);
    }
}
