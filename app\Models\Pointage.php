<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Pointage extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'site_id',
        'debut_pointage',
        'fin_pointage',
        'duree',
        'debut_latitude',
        'debut_longitude',
        'fin_latitude',
        'fin_longitude',
    ];

    protected $casts = [
        'debut_pointage' => 'datetime',
        'fin_pointage' => 'datetime',
        'debut_latitude' => 'decimal:8',
        'debut_longitude' => 'decimal:8',
        'fin_latitude' => 'decimal:8',
        'fin_longitude' => 'decimal:8',
    ];

    /**
     * Get the user that owns the pointage.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the site that owns the pointage.
     */
    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Calculate duration automatically when fin_pointage is set.
     */
    public function calculateDuration(): void
    {
        if ($this->debut_pointage && $this->fin_pointage) {
            $debut = Carbon::parse($this->debut_pointage);
            $fin = Carbon::parse($this->fin_pointage);
            $this->duree = $debut->diff($fin)->format('%H:%I:%S');
        }
    }

    /**
     * Check if pointage is active (not finished).
     */
    public function isActive(): bool
    {
        return is_null($this->fin_pointage);
    }

    // ============ SCOPES POUR OPTIMISATION DES REQUÊTES ============

    /**
     * Scope pour les pointages actifs (non terminés)
     */
    public function scopeActive($query)
    {
        return $query->whereNull('fin_pointage');
    }

    /**
     * Scope pour les pointages terminés
     */
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('fin_pointage');
    }

    /**
     * Scope pour les pointages d'un utilisateur
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope pour les pointages d'un site
     */
    public function scopeForSite($query, int $siteId)
    {
        return $query->where('site_id', $siteId);
    }

    /**
     * Scope pour les pointages dans une plage de dates
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('debut_pointage', [$startDate, $endDate]);
    }

    /**
     * Scope pour les pointages du jour
     */
    public function scopeToday($query)
    {
        return $query->whereDate('debut_pointage', today());
    }

    /**
     * Scope pour les pointages de la semaine
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('debut_pointage', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Scope pour les pointages du mois
     */
    public function scopeThisMonth($query)
    {
        return $query->whereBetween('debut_pointage', [
            now()->startOfMonth(),
            now()->endOfMonth()
        ]);
    }

    /**
     * Scope avec relations optimisées
     */
    public function scopeWithOptimizedRelations($query)
    {
        return $query->with([
            'user:id,name,email',
            'site:id,name,latitude,longitude'
        ]);
    }
}
