<?php

namespace App\Http\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * Trait pour standardiser les réponses API
 * Fournit des méthodes cohérentes pour toutes les réponses JSON
 */
trait ApiResponseTrait
{
    /**
     * Réponse de succès standard
     */
    protected function successResponse(
        $data = null,
        string $message = 'Opération réussie.',
        string $messageAr = 'تمت العملية بنجاح.',
        int $statusCode = 200,
        array $meta = []
    ): JsonResponse {
        $response = [
            'success' => true,
            'message' => $message,
            'message_ar' => $messageAr,
        ];

        if ($data !== null) {
            if ($data instanceof JsonResource || $data instanceof ResourceCollection) {
                $response['data'] = $data;
            } else {
                $response['data'] = $data;
            }
        }

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        $response['timestamp'] = now()->toISOString();

        return response()->json($response, $statusCode);
    }

    /**
     * Réponse d'erreur standard
     */
    protected function errorResponse(
        string $message = 'Une erreur s\'est produite.',
        string $messageAr = 'حدث خطأ.',
        int $statusCode = 400,
        \Exception $exception = null,
        array $errors = []
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
            'message_ar' => $messageAr,
            'timestamp' => now()->toISOString()
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        // En mode debug, ajouter les détails de l'exception
        if (config('app.debug') && $exception) {
            $response['debug'] = [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
            ];
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Réponse de validation échouée
     */
    protected function validationErrorResponse(
        array $errors,
        string $message = 'Données de validation invalides.',
        string $messageAr = 'بيانات التحقق غير صالحة.'
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 422, null, $errors);
    }

    /**
     * Réponse d'authentification requise
     */
    protected function unauthenticatedResponse(
        string $message = 'Authentification requise.',
        string $messageAr = 'المصادقة مطلوبة.'
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 401);
    }

    /**
     * Réponse d'accès non autorisé
     */
    protected function unauthorizedResponse(
        string $message = 'Accès non autorisé.',
        string $messageAr = 'الوصول غير مصرح به.'
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 403);
    }

    /**
     * Réponse de ressource non trouvée
     */
    protected function notFoundResponse(
        string $message = 'Ressource non trouvée.',
        string $messageAr = 'المورد غير موجود.'
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 404);
    }

    /**
     * Réponse de conflit
     */
    protected function conflictResponse(
        string $message = 'Conflit détecté.',
        string $messageAr = 'تم اكتشاف تضارب.'
    ): JsonResponse {
        return $this->errorResponse($message, $messageAr, 409);
    }

    /**
     * Réponse de limite de taux dépassée
     */
    protected function rateLimitResponse(
        int $retryAfter = 60,
        string $message = 'Limite de requêtes dépassée.',
        string $messageAr = 'تم تجاوز حد الطلبات.'
    ): JsonResponse {
        return response()->json([
            'success' => false,
            'message' => $message,
            'message_ar' => $messageAr,
            'retry_after' => $retryAfter,
            'timestamp' => now()->toISOString()
        ], 429);
    }

    /**
     * Réponse de création réussie
     */
    protected function createdResponse(
        $data = null,
        string $message = 'Ressource créée avec succès.',
        string $messageAr = 'تم إنشاء المورد بنجاح.'
    ): JsonResponse {
        return $this->successResponse($data, $message, $messageAr, 201);
    }

    /**
     * Réponse de mise à jour réussie
     */
    protected function updatedResponse(
        $data = null,
        string $message = 'Ressource mise à jour avec succès.',
        string $messageAr = 'تم تحديث المورد بنجاح.'
    ): JsonResponse {
        return $this->successResponse($data, $message, $messageAr, 200);
    }

    /**
     * Réponse de suppression réussie
     */
    protected function deletedResponse(
        string $message = 'Ressource supprimée avec succès.',
        string $messageAr = 'تم حذف المورد بنجاح.'
    ): JsonResponse {
        return $this->successResponse(null, $message, $messageAr, 200);
    }

    /**
     * Réponse avec pagination
     */
    protected function paginatedResponse(
        $data,
        string $message = 'Données récupérées avec succès.',
        string $messageAr = 'تم استرجاع البيانات بنجاح.',
        array $additionalMeta = []
    ): JsonResponse {
        $meta = [
            'current_page' => $data->currentPage(),
            'total' => $data->total(),
            'per_page' => $data->perPage(),
            'last_page' => $data->lastPage(),
            'from' => $data->firstItem(),
            'to' => $data->lastItem(),
        ];

        if (!empty($additionalMeta)) {
            $meta = array_merge($meta, $additionalMeta);
        }

        return $this->successResponse($data->items(), $message, $messageAr, 200, $meta);
    }

    /**
     * Réponse de maintenance
     */
    protected function maintenanceResponse(
        string $message = 'Service en maintenance.',
        string $messageAr = 'الخدمة قيد الصيانة.'
    ): JsonResponse {
        return response()->json([
            'success' => false,
            'message' => $message,
            'message_ar' => $messageAr,
            'maintenance' => true,
            'timestamp' => now()->toISOString()
        ], 503);
    }

    /**
     * Réponse de santé du service
     */
    protected function healthResponse(array $checks = []): JsonResponse
    {
        $allHealthy = empty($checks) || !in_array(false, $checks);
        
        return response()->json([
            'success' => $allHealthy,
            'status' => $allHealthy ? 'healthy' : 'unhealthy',
            'checks' => $checks,
            'timestamp' => now()->toISOString()
        ], $allHealthy ? 200 : 503);
    }
}
