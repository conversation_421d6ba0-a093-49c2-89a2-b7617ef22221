<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test successful login with valid credentials.
     */
    public function test_login_with_valid_credentials(): void
    {
        // Créer un utilisateur de test
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);

        // Tenter de se connecter
        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        // Vérifier la réponse
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'message_ar',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'role'
                        ],
                        'token'
                    ]
                ])
                ->assertJson([
                    'success' => true
                ]);
    }

    /**
     * Test login with invalid credentials.
     */
    public function test_login_with_invalid_credentials(): void
    {
        // Créer un utilisateur de test
        User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);

        // Tenter de se connecter avec un mauvais mot de passe
        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);

        // Vérifier la réponse d'erreur
        $response->assertStatus(401)
                ->assertJson([
                    'success' => false
                ]);
    }

    /**
     * Test login with missing email.
     */
    public function test_login_with_missing_email(): void
    {
        $response = $this->postJson('/api/auth/login', [
            'password' => 'password123'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /**
     * Test login with invalid email format.
     */
    public function test_login_with_invalid_email_format(): void
    {
        $response = $this->postJson('/api/auth/login', [
            'email' => 'invalid-email',
            'password' => 'password123'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /**
     * Test successful logout with valid token.
     */
    public function test_logout_with_valid_token(): void
    {
        // Créer un utilisateur de test
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);

        // Créer un token simple pour ce test
        $token = base64_encode($user->id . '|' . time() . '|' . $user->email);

        // Tenter de se déconnecter avec le token
        $response = $this->postJson('/api/auth/logout', [], [
            'Authorization' => 'Bearer ' . $token
        ]);

        // Vérifier la réponse
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Déconnexion réussie.'
                ]);
    }

    /**
     * Test logout without authentication.
     */
    public function test_logout_without_token(): void
    {
        // Tenter de se déconnecter sans être authentifié
        $response = $this->postJson('/api/auth/logout');

        // Vérifier la réponse d'erreur
        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Token d\'authentification requis.'
                ]);
    }
}
