<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware de sécurité pour ajouter les headers de sécurité
 * Protège contre diverses attaques web
 */
class SecurityHeadersMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Headers de sécurité de base
        $this->addSecurityHeaders($response);

        // Headers spécifiques selon l'environnement
        if (app()->environment('production')) {
            $this->addProductionSecurityHeaders($response);
        }

        return $response;
    }

    /**
     * Ajoute les headers de sécurité de base
     */
    private function addSecurityHeaders(Response $response): void
    {
        // Prévention du clickjacking
        $response->headers->set('X-Frame-Options', 'DENY');

        // Protection contre le sniffing MIME
        $response->headers->set('X-Content-Type-Options', 'nosniff');

        // Protection XSS
        $response->headers->set('X-XSS-Protection', '1; mode=block');

        // Politique de référent
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

        // Permissions Policy (anciennement Feature Policy)
        $response->headers->set('Permissions-Policy', 
            'geolocation=(self), microphone=(), camera=(), payment=(), usb=()'
        );

        // Content Security Policy pour les API
        if (request()->is('api/*')) {
            $response->headers->set('Content-Security-Policy', 
                "default-src 'none'; frame-ancestors 'none';"
            );
        }

        // Headers CORS sécurisés
        $this->addCorsHeaders($response);
    }

    /**
     * Ajoute les headers de sécurité pour la production
     */
    private function addProductionSecurityHeaders(Response $response): void
    {
        // HTTP Strict Transport Security (HSTS)
        if (config('clockin.security.require_https', true)) {
            $response->headers->set('Strict-Transport-Security', 
                'max-age=31536000; includeSubDomains; preload'
            );
        }

        // Expect-CT pour la transparence des certificats
        $response->headers->set('Expect-CT', 
            'max-age=86400, enforce'
        );

        // Cache-Control sécurisé pour les données sensibles
        if (request()->is('api/pointage*') || request()->is('api/auth*')) {
            $response->headers->set('Cache-Control', 
                'no-store, no-cache, must-revalidate, private'
            );
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');
        }
    }

    /**
     * Ajoute les headers CORS sécurisés
     */
    private function addCorsHeaders(Response $response): void
    {
        // Domaines autorisés
        $allowedOrigins = config('cors.allowed_origins', []);
        $origin = request()->header('Origin');

        if (in_array($origin, $allowedOrigins) || in_array('*', $allowedOrigins)) {
            $response->headers->set('Access-Control-Allow-Origin', $origin ?: '*');
        }

        // Méthodes autorisées
        $response->headers->set('Access-Control-Allow-Methods', 
            'GET, POST, PUT, DELETE, OPTIONS'
        );

        // Headers autorisés
        $response->headers->set('Access-Control-Allow-Headers', 
            'Content-Type, Authorization, X-Requested-With, Accept'
        );

        // Credentials
        $response->headers->set('Access-Control-Allow-Credentials', 'true');

        // Durée de cache pour les preflight
        $response->headers->set('Access-Control-Max-Age', '86400');
    }

    /**
     * Vérifie si la requête est sécurisée
     */
    public static function isSecureRequest(Request $request): bool
    {
        return $request->isSecure() || 
               $request->header('X-Forwarded-Proto') === 'https' ||
               app()->environment('local');
    }

    /**
     * Valide les headers de sécurité requis
     */
    public static function validateSecurityHeaders(Request $request): array
    {
        $issues = [];

        // Vérifier HTTPS en production
        if (app()->environment('production') && !self::isSecureRequest($request)) {
            $issues[] = 'HTTPS requis en production';
        }

        // Vérifier User-Agent
        if (!$request->header('User-Agent')) {
            $issues[] = 'User-Agent manquant';
        }

        // Vérifier les headers suspects
        $suspiciousHeaders = [
            'X-Forwarded-For' => 'Possible proxy/load balancer',
            'X-Real-IP' => 'Possible reverse proxy',
        ];

        foreach ($suspiciousHeaders as $header => $description) {
            if ($request->header($header)) {
                $issues[] = "{$header}: {$description}";
            }
        }

        return $issues;
    }

    /**
     * Détecte les tentatives d'attaque communes
     */
    public static function detectAttackPatterns(Request $request): array
    {
        $patterns = [];
        $content = $request->getContent();
        $queryString = $request->getQueryString();
        $userAgent = $request->header('User-Agent', '');

        // Patterns SQL Injection
        $sqlPatterns = [
            '/(\bUNION\b.*\bSELECT\b)/i',
            '/(\bSELECT\b.*\bFROM\b)/i',
            '/(\bINSERT\b.*\bINTO\b)/i',
            '/(\bDELETE\b.*\bFROM\b)/i',
            '/(\bDROP\b.*\bTABLE\b)/i',
        ];

        // Patterns XSS
        $xssPatterns = [
            '/<script[^>]*>.*?<\/script>/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe[^>]*>/i',
        ];

        // Patterns Path Traversal
        $pathTraversalPatterns = [
            '/\.\.\//',
            '/\.\.\\\\/',
            '/%2e%2e%2f/i',
            '/%2e%2e%5c/i',
        ];

        // Vérifier SQL Injection
        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $content) || preg_match($pattern, $queryString)) {
                $patterns[] = 'Tentative SQL Injection détectée';
                break;
            }
        }

        // Vérifier XSS
        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $content) || preg_match($pattern, $queryString)) {
                $patterns[] = 'Tentative XSS détectée';
                break;
            }
        }

        // Vérifier Path Traversal
        foreach ($pathTraversalPatterns as $pattern) {
            if (preg_match($pattern, $request->getPathInfo()) || preg_match($pattern, $queryString)) {
                $patterns[] = 'Tentative Path Traversal détectée';
                break;
            }
        }

        // Vérifier User-Agent suspect
        $suspiciousUserAgents = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'curl',
            'wget',
            'python-requests',
        ];

        foreach ($suspiciousUserAgents as $suspicious) {
            if (stripos($userAgent, $suspicious) !== false) {
                $patterns[] = "User-Agent suspect détecté: {$suspicious}";
                break;
            }
        }

        return $patterns;
    }
}
