<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\User;

class SimpleAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $token = $request->bearerToken();

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Token d\'authentification requis.',
                'message_ar' => 'رمز المصادقة مطلوب.'
            ], 401);
        }

        // Décoder le token simple (format: base64(user_id|timestamp|email))
        try {
            $decoded = base64_decode($token);
            $parts = explode('|', $decoded);

            if (count($parts) !== 3) {
                throw new \Exception('Invalid token format');
            }

            $userId = $parts[0];
            $timestamp = $parts[1];
            $email = $parts[2];

            // Vérifier si le token n'est pas trop ancien (24h)
            if (time() - $timestamp > 86400) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token expiré.',
                    'message_ar' => 'انتهت صلاحية الرمز.'
                ], 401);
            }

            // Récupérer l'utilisateur
            $user = User::find($userId);

            if (!$user || $user->email !== $email) {
                throw new \Exception('User not found');
            }

            // Ajouter l'utilisateur à la requête
            $request->setUserResolver(function () use ($user) {
                return $user;
            });

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Token invalide.',
                'message_ar' => 'رمز غير صالح.'
            ], 401);
        }

        return $next($request);
    }
}
