<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('verifications', function (Blueprint $table) {
            // Supprimer les anciens index et créer les nouveaux avec les bons noms
            $table->dropIndex(['user_id']);
            $table->dropIndex(['date_heure']);

            $table->index('user_id', 'idx_verifications_user');
            $table->index('date_heure', 'idx_verifications_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('verifications', function (Blueprint $table) {
            // Restaurer les anciens index
            $table->dropIndex('idx_verifications_user');
            $table->dropIndex('idx_verifications_date');

            $table->index('user_id');
            $table->index('date_heure');
        });
    }
};
