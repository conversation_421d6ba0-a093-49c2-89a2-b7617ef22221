<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware de monitoring des performances
 * Surveille les temps de réponse et l'utilisation des ressources
 */
class PerformanceMonitoringMiddleware
{
    /**
     * Seuils de performance (en millisecondes)
     */
    private const PERFORMANCE_THRESHOLDS = [
        'warning' => 1000,  // 1 seconde
        'critical' => 3000, // 3 secondes
    ];

    /**
     * Seuil d'utilisation mémoire (en bytes)
     */
    private const MEMORY_THRESHOLD = 128 * 1024 * 1024; // 128MB

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Capturer les métriques de début
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        $startPeakMemory = memory_get_peak_usage(true);

        // Traiter la requête
        $response = $next($request);

        // Capturer les métriques de fin
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $endPeakMemory = memory_get_peak_usage(true);

        // Calculer les métriques
        $metrics = $this->calculateMetrics(
            $startTime,
            $endTime,
            $startMemory,
            $endMemory,
            $startPeakMemory,
            $endPeakMemory
        );

        // Analyser et logger les performances
        $this->analyzePerformance($request, $response, $metrics);

        // Ajouter les headers de performance en mode debug
        if (config('app.debug')) {
            $response->headers->set('X-Response-Time', $metrics['response_time'] . 'ms');
            $response->headers->set('X-Memory-Usage', $this->formatBytes($metrics['memory_used']));
            $response->headers->set('X-Peak-Memory', $this->formatBytes($metrics['peak_memory']));
        }

        return $response;
    }

    /**
     * Calcule les métriques de performance
     */
    private function calculateMetrics(
        float $startTime,
        float $endTime,
        int $startMemory,
        int $endMemory,
        int $startPeakMemory,
        int $endPeakMemory
    ): array {
        return [
            'response_time' => round(($endTime - $startTime) * 1000, 2), // en ms
            'memory_used' => $endMemory - $startMemory,
            'memory_total' => $endMemory,
            'peak_memory' => $endPeakMemory,
            'memory_increase' => $endPeakMemory - $startPeakMemory,
        ];
    }

    /**
     * Analyse les performances et log si nécessaire
     */
    private function analyzePerformance(Request $request, Response $response, array $metrics): void
    {
        $context = [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'status_code' => $response->getStatusCode(),
            'user_id' => $request->user()?->id,
            'ip' => $request->ip(),
            'metrics' => $metrics,
        ];

        // Vérifier les seuils de temps de réponse
        if ($metrics['response_time'] >= self::PERFORMANCE_THRESHOLDS['critical']) {
            Log::channel('performance')->critical('Réponse très lente détectée', $context);
        } elseif ($metrics['response_time'] >= self::PERFORMANCE_THRESHOLDS['warning']) {
            Log::channel('performance')->warning('Réponse lente détectée', $context);
        }

        // Vérifier l'utilisation mémoire
        if ($metrics['peak_memory'] >= self::MEMORY_THRESHOLD) {
            Log::channel('performance')->warning('Utilisation mémoire élevée', $context);
        }

        // Logger les erreurs serveur avec contexte de performance
        if ($response->getStatusCode() >= 500) {
            Log::channel('performance')->error('Erreur serveur avec métriques', $context);
        }

        // Logger les métriques pour les endpoints critiques
        if ($this->isCriticalEndpoint($request)) {
            Log::channel('performance')->info('Métriques endpoint critique', $context);
        }

        // Détecter les fuites mémoire potentielles
        if ($metrics['memory_increase'] > 50 * 1024 * 1024) { // 50MB
            Log::channel('performance')->warning('Augmentation mémoire importante détectée', $context);
        }
    }

    /**
     * Vérifie si l'endpoint est critique
     */
    private function isCriticalEndpoint(Request $request): bool
    {
        $criticalPaths = [
            'api/pointage',
            'api/auth/login',
            'api/pointage/check-location',
        ];

        $path = $request->path();
        
        foreach ($criticalPaths as $criticalPath) {
            if (str_starts_with($path, $criticalPath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Formate les bytes en format lisible
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Obtient un résumé des performances système
     */
    public static function getSystemMetrics(): array
    {
        return [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => ini_get('memory_limit'),
            'execution_time_limit' => ini_get('max_execution_time'),
            'php_version' => PHP_VERSION,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Vérifie la santé du système
     */
    public static function checkSystemHealth(): array
    {
        $metrics = self::getSystemMetrics();
        $health = [];

        // Vérifier l'utilisation mémoire
        $memoryUsagePercent = ($metrics['memory_usage'] / self::parseMemoryLimit($metrics['memory_limit'])) * 100;
        $health['memory'] = [
            'status' => $memoryUsagePercent < 80 ? 'healthy' : ($memoryUsagePercent < 95 ? 'warning' : 'critical'),
            'usage_percent' => round($memoryUsagePercent, 2),
            'usage_bytes' => $metrics['memory_usage'],
        ];

        // Vérifier la version PHP
        $health['php'] = [
            'status' => version_compare(PHP_VERSION, '8.1.0', '>=') ? 'healthy' : 'warning',
            'version' => PHP_VERSION,
        ];

        return $health;
    }

    /**
     * Parse la limite mémoire PHP
     */
    private static function parseMemoryLimit(string $memoryLimit): int
    {
        $memoryLimit = trim($memoryLimit);
        $last = strtolower($memoryLimit[strlen($memoryLimit) - 1]);
        $value = (int) $memoryLimit;

        switch ($last) {
            case 'g':
                $value *= 1024;
                // no break
            case 'm':
                $value *= 1024;
                // no break
            case 'k':
                $value *= 1024;
        }

        return $value;
    }
}
