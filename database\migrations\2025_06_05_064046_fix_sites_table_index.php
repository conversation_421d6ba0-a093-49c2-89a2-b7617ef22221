<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sites', function (Blueprint $table) {
            // Supprimer l'ancien index et créer le nouveau avec le bon nom
            $table->dropIndex(['latitude', 'longitude']);
            $table->index(['latitude', 'longitude'], 'idx_sites_location');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sites', function (Blueprint $table) {
            // Restaurer l'ancien index
            $table->dropIndex('idx_sites_location');
            $table->index(['latitude', 'longitude']);
        });
    }
};
