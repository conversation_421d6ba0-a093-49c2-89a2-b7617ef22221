<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware de limitation de taux professionnel
 * avec différents niveaux selon le type d'opération
 */
class RateLimitMiddleware
{
    /**
     * Limites par défaut (requêtes par minute)
     */
    private const DEFAULT_LIMITS = [
        'login' => 5,           // Tentatives de connexion
        'pointage' => 10,       // Opérations de pointage
        'api' => 60,            // Requêtes API générales
        'admin' => 120,         // Opérations admin
        'export' => 3,          // Exports de données
    ];

    /**
     * Durée de blocage en minutes pour chaque type
     */
    private const BLOCK_DURATION = [
        'login' => 15,          // 15 minutes pour login
        'pointage' => 5,        // 5 minutes pour pointage
        'api' => 1,             // 1 minute pour API
        'admin' => 2,           // 2 minutes pour admin
        'export' => 10,         // 10 minutes pour export
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $type = 'api'): Response
    {
        $identifier = $this->getIdentifier($request);
        $limit = $this->getLimit($type);
        $blockDuration = $this->getBlockDuration($type);

        // Vérifier si l'utilisateur est bloqué
        if ($this->isBlocked($identifier, $type)) {
            $this->logSecurityEvent($request, $type, 'blocked_request');
            return $this->blockedResponse($type);
        }

        // Vérifier la limite de taux
        if ($this->exceedsRateLimit($identifier, $type, $limit)) {
            $this->blockUser($identifier, $type, $blockDuration);
            $this->logSecurityEvent($request, $type, 'rate_limit_exceeded');
            return $this->rateLimitResponse($type, $limit);
        }

        // Incrémenter le compteur
        $this->incrementCounter($identifier, $type);

        return $next($request);
    }

    /**
     * Génère un identifiant unique pour l'utilisateur
     */
    private function getIdentifier(Request $request): string
    {
        // Priorité : utilisateur authentifié > IP
        if ($user = $request->user()) {
            return 'user:' . $user->id;
        }

        return 'ip:' . $request->ip();
    }

    /**
     * Récupère la limite pour un type donné
     */
    private function getLimit(string $type): int
    {
        return config("rate_limit.{$type}", self::DEFAULT_LIMITS[$type] ?? self::DEFAULT_LIMITS['api']);
    }

    /**
     * Récupère la durée de blocage pour un type donné
     */
    private function getBlockDuration(string $type): int
    {
        return self::BLOCK_DURATION[$type] ?? self::BLOCK_DURATION['api'];
    }

    /**
     * Vérifie si l'utilisateur est bloqué
     */
    private function isBlocked(string $identifier, string $type): bool
    {
        $key = "rate_limit:blocked:{$type}:{$identifier}";
        return Cache::has($key);
    }

    /**
     * Vérifie si la limite de taux est dépassée
     */
    private function exceedsRateLimit(string $identifier, string $type, int $limit): bool
    {
        $key = "rate_limit:count:{$type}:{$identifier}";
        $count = Cache::get($key, 0);
        
        return $count >= $limit;
    }

    /**
     * Bloque un utilisateur
     */
    private function blockUser(string $identifier, string $type, int $duration): void
    {
        $key = "rate_limit:blocked:{$type}:{$identifier}";
        Cache::put($key, true, now()->addMinutes($duration));
    }

    /**
     * Incrémente le compteur de requêtes
     */
    private function incrementCounter(string $identifier, string $type): void
    {
        $key = "rate_limit:count:{$type}:{$identifier}";
        
        if (Cache::has($key)) {
            Cache::increment($key);
        } else {
            Cache::put($key, 1, now()->addMinute());
        }
    }

    /**
     * Log les événements de sécurité
     */
    private function logSecurityEvent(Request $request, string $type, string $event): void
    {
        Log::channel('security')->warning("Rate limit event: {$event}", [
            'type' => $type,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => $request->user()?->id,
            'endpoint' => $request->path(),
            'method' => $request->method(),
        ]);
    }

    /**
     * Réponse pour utilisateur bloqué
     */
    private function blockedResponse(string $type): Response
    {
        return response()->json([
            'success' => false,
            'message' => 'Accès temporairement bloqué en raison de trop nombreuses tentatives.',
            'message_ar' => 'تم حظر الوصول مؤقتاً بسبب المحاولات المفرطة.',
            'type' => $type,
            'retry_after' => $this->getBlockDuration($type) * 60
        ], 429);
    }

    /**
     * Réponse pour limite de taux dépassée
     */
    private function rateLimitResponse(string $type, int $limit): Response
    {
        return response()->json([
            'success' => false,
            'message' => "Limite de {$limit} requêtes par minute dépassée.",
            'message_ar' => "تم تجاوز حد {$limit} طلب في الدقيقة.",
            'type' => $type,
            'limit' => $limit,
            'retry_after' => 60
        ], 429);
    }
}
