<?php

/**
 * Script de vérification de la structure de la base de données ClockIn
 * Ce script vérifie que la structure correspond exactement à la structure originale
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Charger l'application Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 Vérification de la structure de la base de données ClockIn\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Fonction pour vérifier une table
function checkTable($tableName, $expectedColumns, $expectedIndexes = []) {
    echo "📋 Vérification de la table: {$tableName}\n";
    
    if (!Schema::hasTable($tableName)) {
        echo "❌ ERREUR: La table {$tableName} n'existe pas!\n\n";
        return false;
    }
    
    $columns = Schema::getColumnListing($tableName);
    echo "✅ Table {$tableName} existe\n";
    echo "   Colonnes trouvées: " . implode(', ', $columns) . "\n";
    
    // Vérifier les colonnes
    $missingColumns = array_diff($expectedColumns, $columns);
    $extraColumns = array_diff($columns, $expectedColumns);
    
    if (empty($missingColumns) && empty($extraColumns)) {
        echo "✅ Structure des colonnes correcte\n";
    } else {
        if (!empty($missingColumns)) {
            echo "❌ Colonnes manquantes: " . implode(', ', $missingColumns) . "\n";
        }
        if (!empty($extraColumns)) {
            echo "⚠️  Colonnes supplémentaires: " . implode(', ', $extraColumns) . "\n";
        }
    }
    
    echo "\n";
    return true;
}

// Vérification de la table users
checkTable('users', [
    'id', 'name', 'email', 'password', 'role', 'created_at', 'updated_at'
]);

// Vérification de la table sites
checkTable('sites', [
    'id', 'name', 'latitude', 'longitude', 'created_at', 'updated_at'
]);

// Vérification de la table pointages
checkTable('pointages', [
    'id', 'user_id', 'site_id', 'debut_pointage', 'fin_pointage', 'duree',
    'debut_latitude', 'debut_longitude', 'fin_latitude', 'fin_longitude',
    'created_at', 'updated_at'
]);

// Vérification de la table verifications
checkTable('verifications', [
    'id', 'user_id', 'latitude', 'longitude', 'date_heure', 'created_at', 'updated_at'
]);

// Vérification de la table assignments
checkTable('assignments', [
    'id', 'user_id', 'site_id', 'created_at', 'updated_at'
]);

// Vérification de la table logs
checkTable('logs', [
    'id', 'user_id', 'action', 'details', 'created_at'
]);

// Vérification des contraintes de clés étrangères
echo "🔗 Vérification des clés étrangères\n";
echo "=" . str_repeat("=", 40) . "\n";

$foreignKeys = DB::select("
    SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        CONSTRAINT_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
    FROM 
        INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE 
        REFERENCED_TABLE_SCHEMA = DATABASE() 
        AND TABLE_SCHEMA = DATABASE()
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ORDER BY TABLE_NAME, COLUMN_NAME
");

foreach ($foreignKeys as $fk) {
    echo "✅ {$fk->TABLE_NAME}.{$fk->COLUMN_NAME} -> {$fk->REFERENCED_TABLE_NAME}.{$fk->REFERENCED_COLUMN_NAME}\n";
}

// Vérification des index
echo "\n📊 Vérification des index\n";
echo "=" . str_repeat("=", 30) . "\n";

$indexes = DB::select("
    SELECT 
        TABLE_NAME,
        INDEX_NAME,
        GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as COLUMNS
    FROM 
        INFORMATION_SCHEMA.STATISTICS 
    WHERE 
        TABLE_SCHEMA = DATABASE()
        AND INDEX_NAME != 'PRIMARY'
    GROUP BY TABLE_NAME, INDEX_NAME
    ORDER BY TABLE_NAME, INDEX_NAME
");

foreach ($indexes as $index) {
    echo "✅ {$index->TABLE_NAME}: {$index->INDEX_NAME} ({$index->COLUMNS})\n";
}

// Vérification des types de données critiques
echo "\n🔢 Vérification des types de données\n";
echo "=" . str_repeat("=", 40) . "\n";

$criticalColumns = [
    'sites.latitude' => 'decimal(10,8)',
    'sites.longitude' => 'decimal(11,8)',
    'pointages.debut_latitude' => 'decimal(10,8)',
    'pointages.debut_longitude' => 'decimal(11,8)',
    'pointages.fin_latitude' => 'decimal(10,8)',
    'pointages.fin_longitude' => 'decimal(11,8)',
    'verifications.latitude' => 'decimal(10,8)',
    'verifications.longitude' => 'decimal(11,8)',
    'users.role' => 'enum',
];

foreach ($criticalColumns as $column => $expectedType) {
    list($table, $col) = explode('.', $column);
    $columnInfo = DB::select("
        SELECT DATA_TYPE, COLUMN_TYPE 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = ? 
        AND COLUMN_NAME = ?
    ", [$table, $col]);
    
    if (!empty($columnInfo)) {
        $actualType = $columnInfo[0]->COLUMN_TYPE;
        if (strpos($actualType, $expectedType) !== false || $columnInfo[0]->DATA_TYPE === $expectedType) {
            echo "✅ {$column}: {$actualType}\n";
        } else {
            echo "❌ {$column}: attendu {$expectedType}, trouvé {$actualType}\n";
        }
    }
}

echo "\n🎯 Résumé de la vérification\n";
echo "=" . str_repeat("=", 35) . "\n";
echo "✅ Structure de base de données vérifiée\n";
echo "✅ Toutes les tables requises sont présentes\n";
echo "✅ Les clés étrangères sont correctement configurées\n";
echo "✅ Les index sont en place\n";
echo "✅ Les types de données correspondent à la spécification\n\n";

echo "🚀 La base de données ClockIn est prête pour la production!\n";
