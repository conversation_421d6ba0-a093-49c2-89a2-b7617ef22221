<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assignments', function (Blueprint $table) {
            // Supprimer l'ancienne contrainte unique et créer la nouvelle avec le bon nom
            $table->dropUnique(['user_id', 'site_id']);
            $table->unique(['user_id', 'site_id'], 'uk_assignments_user_site');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assignments', function (Blueprint $table) {
            // Restaurer l'ancienne contrainte unique
            $table->dropUnique('uk_assignments_user_site');
            $table->unique(['user_id', 'site_id']);
        });
    }
};
