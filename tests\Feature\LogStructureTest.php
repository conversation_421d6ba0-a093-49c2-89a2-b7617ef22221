<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Log;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class LogStructureTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test que la table logs a la structure correcte.
     */
    public function test_logs_table_structure(): void
    {
        // Vérifier que la table logs existe
        $this->assertTrue(Schema::hasTable('logs'));

        // Vérifier les colonnes
        $expectedColumns = ['id', 'user_id', 'action', 'details', 'created_at'];
        $actualColumns = Schema::getColumnListing('logs');

        foreach ($expectedColumns as $column) {
            $this->assertContains($column, $actualColumns, "La colonne {$column} doit exister dans la table logs");
        }

        // Vérifier qu'il n'y a PAS de colonne updated_at
        $this->assertNotContains('updated_at', $actualColumns, "La table logs ne doit PAS avoir de colonne updated_at");
    }

    /**
     * Test de création d'un log avec la nouvelle structure.
     */
    public function test_log_creation_with_correct_structure(): void
    {
        // Créer un utilisateur de test
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);

        // Créer un log
        $log = Log::create([
            'user_id' => $user->id,
            'action' => 'test_action',
            'details' => 'Test de la structure des logs',
            'created_at' => now()
        ]);

        // Vérifier que le log a été créé
        $this->assertDatabaseHas('logs', [
            'user_id' => $user->id,
            'action' => 'test_action',
            'details' => 'Test de la structure des logs'
        ]);

        // Vérifier que created_at est défini
        $this->assertNotNull($log->created_at);

        // Vérifier que le log n'a pas de updated_at
        $this->assertNull($log->updated_at);
    }

    /**
     * Test que les logs sont automatiquement créés lors des actions API.
     */
    public function test_logs_created_during_api_actions(): void
    {
        // Créer un utilisateur admin
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);

        // Compter les logs avant
        $logCountBefore = Log::count();

        // Faire une requête de login
        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $response->assertStatus(200);

        // Vérifier qu'un log a été créé
        $this->assertEquals($logCountBefore + 1, Log::count());

        // Vérifier le contenu du log
        $log = Log::latest()->first();
        $this->assertEquals($admin->id, $log->user_id);
        $this->assertEquals('login_success', $log->action);
        $this->assertNotNull($log->created_at);
        $this->assertNull($log->updated_at);
    }
}
