<?php

namespace App\Http\Controllers\Site;

use App\Http\Controllers\Controller;
use App\Http\Resources\SiteResource;
use App\Models\Site;
use App\Models\User;
use App\Models\Assignment;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * @group Sites Management
 *
 * APIs pour la gestion des sites/chantiers (Admin uniquement)
 */
class SiteController extends Controller
{
    /**
     * List Sites
     *
     * Récupère la liste de tous les sites
     *
     * @authenticated
     *
     * @queryParam page integer Numéro de page pour la pagination. Example: 1
     * @queryParam per_page integer Nombre d'éléments par page (max 50). Example: 15
     * @queryParam search string Recherche par nom de site. Example: Centre-ville
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "data": [
     *       {
     *         "id": 1,
     *         "name": "Chantier Centre-ville",
     *         "latitude": 33.5731,
     *         "longitude": -7.5898,
     *         "users": [...],
     *         "pointages_count": 5,
     *         "created_at": "2025-06-05 08:00:00"
     *       }
     *     ],
     *     "current_page": 1,
     *     "total": 3,
     *     "per_page": 15
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 50);
        $search = $request->get('search');

        $query = Site::with(['users', 'pointages'])
            ->withCount('pointages');

        if ($search) {
            $query->where('name', 'like', "%{$search}%");
        }

        $sites = $query->paginate($perPage);

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'sites_list',
            'details' => 'Consultation de la liste des sites'
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'data' => SiteResource::collection($sites->items()),
                'current_page' => $sites->currentPage(),
                'total' => $sites->total(),
                'per_page' => $sites->perPage(),
                'last_page' => $sites->lastPage()
            ]
        ]);
    }

    /**
     * Create Site
     *
     * Crée un nouveau site/chantier
     *
     * @authenticated
     *
     * @bodyParam name string required Le nom du site. Example: Nouveau Chantier
     * @bodyParam latitude numeric required La latitude du site. Example: 33.5731
     * @bodyParam longitude numeric required La longitude du site. Example: -7.5898
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Site créé avec succès",
     *   "message_ar": "تم إنشاء الموقع بنجاح",
     *   "data": {
     *     "id": 4,
     *     "name": "Nouveau Chantier",
     *     "latitude": 33.5731,
     *     "longitude": -7.5898,
     *     "created_at": "2025-06-05 08:00:00"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:sites,name',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ], [
            'name.required' => 'Le nom du site est requis. / اسم الموقع مطلوب.',
            'name.unique' => 'Ce nom de site existe déjà. / اسم الموقع موجود بالفعل.',
            'latitude.required' => 'La latitude est requise. / خط العرض مطلوب.',
            'latitude.between' => 'La latitude doit être entre -90 et 90. / يجب أن يكون خط العرض بين -90 و 90.',
            'longitude.required' => 'La longitude est requise. / خط الطول مطلوب.',
            'longitude.between' => 'La longitude doit être entre -180 et 180. / يجب أن يكون خط الطول بين -180 و 180.',
        ]);

        $site = Site::create([
            'name' => $request->name,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
        ]);

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'site_created',
            'details' => "Création du site: {$site->name}"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Site créé avec succès.',
            'message_ar' => 'تم إنشاء الموقع بنجاح.',
            'data' => new SiteResource($site)
        ], 201);
    }

    /**
     * Show Site
     *
     * Affiche les détails d'un site spécifique
     *
     * @authenticated
     *
     * @urlParam site integer required L'ID du site. Example: 1
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "id": 1,
     *     "name": "Chantier Centre-ville",
     *     "latitude": 33.5731,
     *     "longitude": -7.5898,
     *     "users": [...],
     *     "pointages_count": 5,
     *     "created_at": "2025-06-05 08:00:00"
     *   }
     * }
     */
    public function show(Site $site): JsonResponse
    {
        $site->load(['users', 'pointages']);
        $site->loadCount('pointages');

        return response()->json([
            'success' => true,
            'data' => new SiteResource($site)
        ]);
    }

    /**
     * Update Site
     *
     * Met à jour un site existant
     *
     * @authenticated
     *
     * @urlParam site integer required L'ID du site. Example: 1
     * @bodyParam name string required Le nom du site. Example: Chantier Modifié
     * @bodyParam latitude numeric required La latitude du site. Example: 33.5731
     * @bodyParam longitude numeric required La longitude du site. Example: -7.5898
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Site mis à jour avec succès",
     *   "message_ar": "تم تحديث الموقع بنجاح",
     *   "data": {
     *     "id": 1,
     *     "name": "Chantier Modifié",
     *     "latitude": 33.5731,
     *     "longitude": -7.5898,
     *     "updated_at": "2025-06-05 08:30:00"
     *   }
     * }
     */
    public function update(Request $request, Site $site): JsonResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('sites')->ignore($site->id)],
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ], [
            'name.required' => 'Le nom du site est requis. / اسم الموقع مطلوب.',
            'name.unique' => 'Ce nom de site existe déjà. / اسم الموقع موجود بالفعل.',
            'latitude.required' => 'La latitude est requise. / خط العرض مطلوب.',
            'latitude.between' => 'La latitude doit être entre -90 et 90. / يجب أن يكون خط العرض بين -90 و 90.',
            'longitude.required' => 'La longitude est requise. / خط الطول مطلوب.',
            'longitude.between' => 'La longitude doit être entre -180 et 180. / يجب أن يكون خط الطول بين -180 و 180.',
        ]);

        $oldName = $site->name;

        $site->update([
            'name' => $request->name,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
        ]);

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'site_updated',
            'details' => "Modification du site: {$oldName} -> {$site->name}"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Site mis à jour avec succès.',
            'message_ar' => 'تم تحديث الموقع بنجاح.',
            'data' => new SiteResource($site)
        ]);
    }

    /**
     * Delete Site
     *
     * Supprime un site (seulement si aucun pointage n'y est associé)
     *
     * @authenticated
     *
     * @urlParam site integer required L'ID du site. Example: 1
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Site supprimé avec succès",
     *   "message_ar": "تم حذف الموقع بنجاح"
     * }
     *
     * @response 400 {
     *   "success": false,
     *   "message": "Impossible de supprimer ce site car il contient des pointages",
     *   "message_ar": "لا يمكن حذف هذا الموقع لأنه يحتوي على تسجيلات حضور"
     * }
     */
    public function destroy(Request $request, Site $site): JsonResponse
    {
        // Vérifier s'il y a des pointages associés
        if ($site->pointages()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer ce site car il contient des pointages.',
                'message_ar' => 'لا يمكن حذف هذا الموقع لأنه يحتوي على تسجيلات حضور.'
            ], 400);
        }

        $siteName = $site->name;
        $site->delete();

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'site_deleted',
            'details' => "Suppression du site: {$siteName}"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Site supprimé avec succès.',
            'message_ar' => 'تم حذف الموقع بنجاح.'
        ]);
    }

    /**
     * Assign Site
     *
     * Assigne des employés à un site
     *
     * @authenticated
     *
     * @bodyParam site_id integer required L'ID du site. Example: 1
     * @bodyParam user_ids array required Liste des IDs des employés. Example: [2, 3, 4]
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Employés assignés avec succès",
     *   "message_ar": "تم تعيين الموظفين بنجاح",
     *   "data": {
     *     "site": {...},
     *     "assigned_users": [...],
     *     "assignments_count": 3
     *   }
     * }
     */
    public function assignSite(Request $request): JsonResponse
    {
        $request->validate([
            'site_id' => 'required|exists:sites,id',
            'user_ids' => 'required|array|min:1',
            'user_ids.*' => 'exists:users,id',
        ], [
            'site_id.required' => 'L\'ID du site est requis. / معرف الموقع مطلوب.',
            'site_id.exists' => 'Le site spécifié n\'existe pas. / الموقع المحدد غير موجود.',
            'user_ids.required' => 'La liste des employés est requise. / قائمة الموظفين مطلوبة.',
            'user_ids.array' => 'La liste des employés doit être un tableau. / يجب أن تكون قائمة الموظفين مصفوفة.',
            'user_ids.*.exists' => 'Un ou plusieurs employés spécifiés n\'existent pas. / واحد أو أكثر من الموظفين المحددين غير موجود.',
        ]);

        $site = Site::findOrFail($request->site_id);
        $userIds = $request->user_ids;

        // Vérifier que tous les utilisateurs sont des employés
        $employees = User::whereIn('id', $userIds)->where('role', 'employee')->get();

        if ($employees->count() !== count($userIds)) {
            return response()->json([
                'success' => false,
                'message' => 'Seuls les employés peuvent être assignés à un site.',
                'message_ar' => 'يمكن تعيين الموظفين فقط لموقع.'
            ], 400);
        }

        // Supprimer les anciennes assignations pour ce site
        Assignment::where('site_id', $site->id)->delete();

        // Créer les nouvelles assignations
        $assignments = [];
        foreach ($userIds as $userId) {
            $assignments[] = [
                'site_id' => $site->id,
                'user_id' => $userId,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        Assignment::insert($assignments);

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'site_assignment',
            'details' => "Assignation de " . count($userIds) . " employés au site: {$site->name}"
        ]);

        $site->load('users');

        return response()->json([
            'success' => true,
            'message' => 'Employés assignés avec succès.',
            'message_ar' => 'تم تعيين الموظفين بنجاح.',
            'data' => [
                'site' => new SiteResource($site),
                'assigned_users' => $employees->pluck('name'),
                'assignments_count' => count($userIds)
            ]
        ]);
    }
}
