<?php

namespace App\Services;

use App\Models\Log;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log as LaravelLog;
use Carbon\Carbon;

/**
 * Service de logging d'audit professionnel
 * Centralise tous les logs métier avec contexte enrichi
 */
class AuditLogService
{
    /**
     * Types d'événements métier
     */
    public const EVENT_TYPES = [
        // Authentification
        'LOGIN_SUCCESS' => 'login_success',
        'LOGIN_FAILED' => 'login_failed',
        'LOGOUT' => 'logout',
        'TOKEN_EXPIRED' => 'token_expired',
        
        // Pointage
        'POINTAGE_START' => 'pointage_start',
        'POINTAGE_END' => 'pointage_end',
        'POINTAGE_FAILED' => 'pointage_failed',
        'LOCATION_CHECK' => 'location_check',
        'LOCATION_DENIED' => 'location_denied',
        
        // Administration
        'USER_CREATED' => 'user_created',
        'USER_UPDATED' => 'user_updated',
        'USER_DELETED' => 'user_deleted',
        'SITE_CREATED' => 'site_created',
        'SITE_UPDATED' => 'site_updated',
        'SITE_DELETED' => 'site_deleted',
        'ASSIGNMENT_CREATED' => 'assignment_created',
        'ASSIGNMENT_DELETED' => 'assignment_deleted',
        
        // Sécurité
        'SECURITY_VIOLATION' => 'security_violation',
        'INTRUSION_DETECTED' => 'intrusion_detected',
        'RATE_LIMIT_EXCEEDED' => 'rate_limit_exceeded',
        'SUSPICIOUS_ACTIVITY' => 'suspicious_activity',
        
        // Système
        'DATA_EXPORT' => 'data_export',
        'SYSTEM_ERROR' => 'system_error',
        'PERFORMANCE_ISSUE' => 'performance_issue',
    ];

    /**
     * Niveaux de criticité
     */
    public const SEVERITY_LEVELS = [
        'LOW' => 'low',
        'MEDIUM' => 'medium',
        'HIGH' => 'high',
        'CRITICAL' => 'critical',
    ];

    /**
     * Log un événement d'authentification
     */
    public function logAuthentication(
        string $eventType,
        ?User $user = null,
        ?Request $request = null,
        array $additionalData = []
    ): void {
        $this->logEvent($eventType, $user, $request, array_merge([
            'category' => 'authentication',
            'severity' => $eventType === self::EVENT_TYPES['LOGIN_FAILED'] ? self::SEVERITY_LEVELS['MEDIUM'] : self::SEVERITY_LEVELS['LOW'],
        ], $additionalData));
    }

    /**
     * Log un événement de pointage
     */
    public function logPointage(
        string $eventType,
        User $user,
        ?Request $request = null,
        array $pointageData = []
    ): void {
        $this->logEvent($eventType, $user, $request, [
            'category' => 'pointage',
            'severity' => self::SEVERITY_LEVELS['LOW'],
            'pointage_data' => $pointageData,
        ]);
    }

    /**
     * Log un événement d'administration
     */
    public function logAdministration(
        string $eventType,
        User $admin,
        ?Request $request = null,
        array $targetData = []
    ): void {
        $this->logEvent($eventType, $admin, $request, [
            'category' => 'administration',
            'severity' => self::SEVERITY_LEVELS['MEDIUM'],
            'target_data' => $targetData,
        ]);
    }

    /**
     * Log un événement de sécurité
     */
    public function logSecurity(
        string $eventType,
        ?User $user = null,
        ?Request $request = null,
        array $securityData = []
    ): void {
        $severity = match($eventType) {
            self::EVENT_TYPES['INTRUSION_DETECTED'] => self::SEVERITY_LEVELS['CRITICAL'],
            self::EVENT_TYPES['SECURITY_VIOLATION'] => self::SEVERITY_LEVELS['HIGH'],
            default => self::SEVERITY_LEVELS['MEDIUM']
        };

        $this->logEvent($eventType, $user, $request, [
            'category' => 'security',
            'severity' => $severity,
            'security_data' => $securityData,
        ]);
    }

    /**
     * Log un événement système
     */
    public function logSystem(
        string $eventType,
        ?User $user = null,
        ?Request $request = null,
        array $systemData = []
    ): void {
        $severity = match($eventType) {
            self::EVENT_TYPES['SYSTEM_ERROR'] => self::SEVERITY_LEVELS['HIGH'],
            self::EVENT_TYPES['PERFORMANCE_ISSUE'] => self::SEVERITY_LEVELS['MEDIUM'],
            default => self::SEVERITY_LEVELS['LOW']
        };

        $this->logEvent($eventType, $user, $request, [
            'category' => 'system',
            'severity' => $severity,
            'system_data' => $systemData,
        ]);
    }

    /**
     * Log un événement générique avec contexte enrichi
     */
    private function logEvent(
        string $eventType,
        ?User $user = null,
        ?Request $request = null,
        array $additionalData = []
    ): void {
        try {
            // Préparer les données de base
            $logData = [
                'event_type' => $eventType,
                'timestamp' => Carbon::now()->toISOString(),
                'user_context' => $this->getUserContext($user),
                'request_context' => $this->getRequestContext($request),
                'system_context' => $this->getSystemContext(),
            ];

            // Fusionner avec les données additionnelles
            $logData = array_merge($logData, $additionalData);

            // Enregistrer dans la table logs
            $this->saveToDatabase($eventType, $user, $logData);

            // Enregistrer dans les logs Laravel selon la criticité
            $this->saveToLaravelLogs($eventType, $logData);

        } catch (\Exception $e) {
            // En cas d'erreur de logging, utiliser le log d'urgence
            LaravelLog::emergency('Erreur dans le système de logging d\'audit', [
                'original_event' => $eventType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Obtient le contexte utilisateur
     */
    private function getUserContext(?User $user): array
    {
        if (!$user) {
            return ['authenticated' => false];
        }

        return [
            'authenticated' => true,
            'user_id' => $user->id,
            'user_name' => $user->name,
            'user_email' => $user->email,
            'user_role' => $user->role,
        ];
    }

    /**
     * Obtient le contexte de la requête
     */
    private function getRequestContext(?Request $request): array
    {
        if (!$request) {
            return ['has_request' => false];
        }

        return [
            'has_request' => true,
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'endpoint' => $request->path(),
            'referer' => $request->header('Referer'),
            'accept_language' => $request->header('Accept-Language'),
        ];
    }

    /**
     * Obtient le contexte système
     */
    private function getSystemContext(): array
    {
        return [
            'app_env' => app()->environment(),
            'app_version' => config('app.version', '1.0.0'),
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'server_time' => Carbon::now()->toISOString(),
        ];
    }

    /**
     * Sauvegarde dans la base de données
     */
    private function saveToDatabase(string $eventType, ?User $user, array $logData): void
    {
        Log::create([
            'user_id' => $user?->id,
            'action' => $eventType,
            'details' => json_encode($logData, JSON_UNESCAPED_UNICODE),
            'created_at' => Carbon::now(),
        ]);
    }

    /**
     * Sauvegarde dans les logs Laravel
     */
    private function saveToLaravelLogs(string $eventType, array $logData): void
    {
        $severity = $logData['severity'] ?? self::SEVERITY_LEVELS['LOW'];
        $category = $logData['category'] ?? 'general';
        
        $channel = match($category) {
            'authentication' => 'security',
            'pointage' => 'pointage',
            'administration' => 'api',
            'security' => 'security',
            'system' => 'performance',
            default => 'single'
        };

        $logLevel = match($severity) {
            self::SEVERITY_LEVELS['CRITICAL'] => 'critical',
            self::SEVERITY_LEVELS['HIGH'] => 'error',
            self::SEVERITY_LEVELS['MEDIUM'] => 'warning',
            default => 'info'
        };

        LaravelLog::channel($channel)->log($logLevel, "Événement: {$eventType}", $logData);
    }

    /**
     * Recherche dans les logs d'audit
     */
    public function searchLogs(array $criteria = [], int $limit = 100): \Illuminate\Support\Collection
    {
        $query = Log::query();

        if (isset($criteria['user_id'])) {
            $query->where('user_id', $criteria['user_id']);
        }

        if (isset($criteria['action'])) {
            $query->where('action', $criteria['action']);
        }

        if (isset($criteria['date_from'])) {
            $query->where('created_at', '>=', $criteria['date_from']);
        }

        if (isset($criteria['date_to'])) {
            $query->where('created_at', '<=', $criteria['date_to']);
        }

        if (isset($criteria['category'])) {
            $query->whereRaw("JSON_EXTRACT(details, '$.category') = ?", [$criteria['category']]);
        }

        if (isset($criteria['severity'])) {
            $query->whereRaw("JSON_EXTRACT(details, '$.severity') = ?", [$criteria['severity']]);
        }

        return $query->orderBy('created_at', 'desc')
                    ->limit($limit)
                    ->get();
    }

    /**
     * Obtient les statistiques des logs
     */
    public function getLogStatistics(Carbon $from, Carbon $to): array
    {
        $logs = Log::whereBetween('created_at', [$from, $to])->get();

        $stats = [
            'total_events' => $logs->count(),
            'by_category' => [],
            'by_severity' => [],
            'by_user' => [],
            'by_day' => [],
        ];

        foreach ($logs as $log) {
            $details = json_decode($log->details, true) ?? [];
            
            // Par catégorie
            $category = $details['category'] ?? 'unknown';
            $stats['by_category'][$category] = ($stats['by_category'][$category] ?? 0) + 1;
            
            // Par sévérité
            $severity = $details['severity'] ?? 'unknown';
            $stats['by_severity'][$severity] = ($stats['by_severity'][$severity] ?? 0) + 1;
            
            // Par utilisateur
            if ($log->user_id) {
                $stats['by_user'][$log->user_id] = ($stats['by_user'][$log->user_id] ?? 0) + 1;
            }
            
            // Par jour
            $day = $log->created_at->format('Y-m-d');
            $stats['by_day'][$day] = ($stats['by_day'][$day] ?? 0) + 1;
        }

        return $stats;
    }
}
