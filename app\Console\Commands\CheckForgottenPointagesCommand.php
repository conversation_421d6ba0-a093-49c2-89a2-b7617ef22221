<?php

namespace App\Console\Commands;

use App\Services\PointageService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

/**
 * Commande pour vérifier les pointages oubliés
 */
class CheckForgottenPointagesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'clockin:check-forgotten 
                            {--notify : Envoyer des notifications}
                            {--hours= : Nombre d\'heures avant qu\'un pointage soit considéré comme oublié (défaut: 12)}';

    /**
     * The console command description.
     */
    protected $description = 'Vérifie les pointages oubliés (non terminés depuis longtemps)';

    public function __construct(
        private PointageService $pointageService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Vérification des pointages oubliés...');

        $hours = (int) $this->option('hours') ?: config('clockin.monitoring.forgotten_pointage_hours', 12);
        $notify = $this->option('notify');

        $forgotten = $this->pointageService->checkForgottenPointages();

        if (empty($forgotten)) {
            $this->info('✅ Aucun pointage oublié détecté');
            return 0;
        }

        $this->warn("⚠️  {count($forgotten)} pointage(s) oublié(s) détecté(s):");

        // Afficher les détails
        $this->table(
            ['ID Pointage', 'Utilisateur', 'Site', 'Début', 'Heures écoulées'],
            array_map(function ($item) {
                return [
                    $item['pointage_id'],
                    $item['user'],
                    $item['site'],
                    $item['started_at']->format('Y-m-d H:i:s'),
                    $item['hours_ago'] . 'h'
                ];
            }, $forgotten)
        );

        // Log l'événement
        Log::channel('pointage')->warning('Pointages oubliés détectés', [
            'count' => count($forgotten),
            'details' => $forgotten
        ]);

        // Envoyer des notifications si demandé
        if ($notify) {
            $this->sendNotifications($forgotten);
        }

        return 0;
    }

    /**
     * Envoie des notifications pour les pointages oubliés
     */
    private function sendNotifications(array $forgotten): void
    {
        $this->info('📧 Envoi des notifications...');

        try {
            // Ici, vous pourriez implémenter l'envoi de notifications
            // par email, Slack, ou autres canaux
            
            foreach ($forgotten as $item) {
                // Exemple de notification par email à l'admin
                // Notification::route('mail', config('mail.admin_email'))
                //     ->notify(new ForgottenPointageNotification($item));
                
                $this->line("  - Notification envoyée pour {$item['user']}");
            }

            $this->info('✅ Notifications envoyées');

        } catch (\Exception $e) {
            $this->error('❌ Erreur lors de l\'envoi des notifications: ' . $e->getMessage());
            Log::error('Erreur envoi notifications pointages oubliés', [
                'error' => $e->getMessage(),
                'forgotten_count' => count($forgotten)
            ]);
        }
    }
}
