<?php

namespace Tests\Feature\Api;

use App\Models\Pointage;
use App\Models\Site;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

/**
 * Tests d'intégration pour l'API de pointage
 */
class PointageApiTest extends TestCase
{
    use RefreshDatabase;

    private User $employee;
    private User $admin;
    private Site $site;

    protected function setUp(): void
    {
        parent::setUp();

        // Créer les utilisateurs de test
        $this->employee = User::factory()->create([
            'role' => 'employee',
            'name' => 'Test Employee',
            'email' => '<EMAIL>',
        ]);

        $this->admin = User::factory()->create([
            'role' => 'admin',
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
        ]);

        // Créer un site de test
        $this->site = Site::factory()->create([
            'name' => 'Site Test',
            'latitude' => 33.5731,
            'longitude' => -7.5898,
        ]);

        // Créer l'assignation
        \DB::table('assignments')->insert([
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Vider le cache
        Cache::flush();
    }

    /**
     * Test de pointage réussi (début)
     */
    public function test_successful_pointage_start(): void
    {
        Sanctum::actingAs($this->employee);

        $response = $this->postJson('/api/pointage', [
            'latitude' => 33.5732,  // Très proche du site
            'longitude' => -7.5899,
            'accuracy' => 10,
        ]);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'message',
                    'message_ar',
                    'data' => [
                        'id',
                        'user_id',
                        'site_id',
                        'debut_pointage',
                        'debut_latitude',
                        'debut_longitude',
                    ],
                ]);

        $this->assertDatabaseHas('pointages', [
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id,
            'debut_latitude' => 33.5732,
            'debut_longitude' => -7.5899,
        ]);
    }

    /**
     * Test de pointage réussi (fin)
     */
    public function test_successful_pointage_end(): void
    {
        // Créer un pointage actif
        $pointage = Pointage::factory()->create([
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id,
            'debut_pointage' => Carbon::now()->subHours(2),
            'fin_pointage' => null,
        ]);

        Sanctum::actingAs($this->employee);

        $response = $this->postJson('/api/pointage', [
            'latitude' => 33.5733,
            'longitude' => -7.5897,
            'accuracy' => 15,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'data' => [
                        'fin_pointage',
                        'fin_latitude',
                        'fin_longitude',
                        'duree',
                    ],
                ]);

        $this->assertDatabaseHas('pointages', [
            'id' => $pointage->id,
            'fin_latitude' => 33.5733,
            'fin_longitude' => -7.5897,
        ]);
    }

    /**
     * Test de pointage refusé (hors zone)
     */
    public function test_pointage_denied_out_of_range(): void
    {
        Sanctum::actingAs($this->employee);

        $response = $this->postJson('/api/pointage', [
            'latitude' => 34.0209,  // Rabat (loin du site)
            'longitude' => -6.8416,
            'accuracy' => 10,
        ]);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                ])
                ->assertJsonStructure([
                    'success',
                    'message',
                    'message_ar',
                    'distance',
                ]);

        $this->assertDatabaseMissing('pointages', [
            'user_id' => $this->employee->id,
            'debut_latitude' => 34.0209,
        ]);
    }

    /**
     * Test de pointage sans authentification
     */
    public function test_pointage_unauthenticated(): void
    {
        $response = $this->postJson('/api/pointage', [
            'latitude' => 33.5732,
            'longitude' => -7.5899,
        ]);

        $response->assertStatus(401);
    }

    /**
     * Test de pointage avec données invalides
     */
    public function test_pointage_invalid_data(): void
    {
        Sanctum::actingAs($this->employee);

        $response = $this->postJson('/api/pointage', [
            'latitude' => 'invalid',
            'longitude' => 200, // Longitude invalide
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['latitude', 'longitude']);
    }

    /**
     * Test de pointage sans assignation
     */
    public function test_pointage_no_assignment(): void
    {
        // Créer un utilisateur sans assignation
        $userWithoutSite = User::factory()->create(['role' => 'employee']);
        
        Sanctum::actingAs($userWithoutSite);

        $response = $this->postJson('/api/pointage', [
            'latitude' => 33.5732,
            'longitude' => -7.5899,
        ]);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Aucun site assigné à cet utilisateur.',
                ]);
    }

    /**
     * Test de liste des pointages (admin)
     */
    public function test_admin_can_list_pointages(): void
    {
        // Créer quelques pointages
        Pointage::factory()->count(3)->create([
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id,
        ]);

        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/pointage');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'data',
                    'meta' => [
                        'current_page',
                        'total',
                        'per_page',
                        'last_page',
                    ],
                ]);
    }

    /**
     * Test de liste des pointages (employé non autorisé)
     */
    public function test_employee_cannot_list_pointages(): void
    {
        Sanctum::actingAs($this->employee);

        $response = $this->getJson('/api/pointage');

        $response->assertStatus(403);
    }

    /**
     * Test de filtrage des pointages par date
     */
    public function test_filter_pointages_by_date(): void
    {
        // Créer des pointages à différentes dates
        Pointage::factory()->create([
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id,
            'debut_pointage' => Carbon::parse('2024-01-01'),
        ]);

        Pointage::factory()->create([
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id,
            'debut_pointage' => Carbon::parse('2024-01-15'),
        ]);

        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/pointage?' . http_build_query([
            'date_debut' => '2024-01-01',
            'date_fin' => '2024-01-10',
        ]));

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertCount(1, $data);
    }

    /**
     * Test d'export des pointages
     */
    public function test_export_pointages(): void
    {
        // Créer quelques pointages
        Pointage::factory()->count(5)->create([
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id,
        ]);

        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/pointage/export');

        $response->assertStatus(200);
        $this->assertEquals('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
                           $response->headers->get('content-type'));
    }

    /**
     * Test de rate limiting
     */
    public function test_rate_limiting(): void
    {
        Sanctum::actingAs($this->employee);

        // Faire beaucoup de requêtes rapidement
        for ($i = 0; $i < 15; $i++) {
            $response = $this->postJson('/api/pointage', [
                'latitude' => 33.5732,
                'longitude' => -7.5899,
            ]);

            if ($i < 10) {
                // Les premières requêtes devraient passer
                $this->assertNotEquals(429, $response->getStatusCode());
            }
        }

        // La dernière requête devrait être limitée
        $response = $this->postJson('/api/pointage', [
            'latitude' => 33.5732,
            'longitude' => -7.5899,
        ]);

        $response->assertStatus(429);
    }

    /**
     * Test de validation anti-fraude
     */
    public function test_anti_fraud_validation(): void
    {
        Sanctum::actingAs($this->employee);

        // Premier pointage
        $this->postJson('/api/pointage', [
            'latitude' => 33.5732,
            'longitude' => -7.5899,
        ])->assertStatus(201);

        // Deuxième pointage immédiatement à une distance importante
        $response = $this->postJson('/api/pointage', [
            'latitude' => 34.0209, // Rabat
            'longitude' => -6.8416,
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['coordinates']);
    }

    /**
     * Test de performance avec beaucoup de données
     */
    public function test_performance_with_large_dataset(): void
    {
        // Créer beaucoup de pointages
        Pointage::factory()->count(1000)->create([
            'site_id' => $this->site->id,
        ]);

        Sanctum::actingAs($this->admin);

        $startTime = microtime(true);

        $response = $this->getJson('/api/pointage?per_page=50');

        $endTime = microtime(true);
        $duration = $endTime - $startTime;

        $response->assertStatus(200);
        
        // La requête devrait être rapide même avec beaucoup de données
        $this->assertLessThan(2.0, $duration);
    }

    /**
     * Test de headers de sécurité
     */
    public function test_security_headers(): void
    {
        Sanctum::actingAs($this->employee);

        $response = $this->postJson('/api/pointage', [
            'latitude' => 33.5732,
            'longitude' => -7.5899,
        ]);

        $response->assertHeader('X-Content-Type-Options', 'nosniff');
        $response->assertHeader('X-Frame-Options', 'DENY');
        $response->assertHeader('X-XSS-Protection', '1; mode=block');
    }

    /**
     * Test de logging des événements
     */
    public function test_event_logging(): void
    {
        Sanctum::actingAs($this->employee);

        $this->postJson('/api/pointage', [
            'latitude' => 33.5732,
            'longitude' => -7.5899,
        ]);

        // Vérifier que l'événement a été loggé
        $this->assertDatabaseHas('logs', [
            'user_id' => $this->employee->id,
            'action' => 'pointage_start',
        ]);
    }
}
