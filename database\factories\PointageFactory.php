<?php

namespace Database\Factories;

use App\Models\Pointage;
use App\Models\Site;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Factory pour le modèle Pointage
 */
class PointageFactory extends Factory
{
    protected $model = Pointage::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $debutPointage = $this->faker->dateTimeBetween('-30 days', 'now');
        $finPointage = $this->faker->boolean(80) ? // 80% de chance d'avoir une fin
            Carbon::instance($debutPointage)->addHours($this->faker->numberBetween(1, 12)) :
            null;

        // Coordonnées aléatoires proches du Maroc
        $latitude = $this->faker->randomFloat(6, 30.0, 36.0);
        $longitude = $this->faker->randomFloat(6, -10.0, -1.0);

        return [
            'user_id' => User::factory(),
            'site_id' => Site::factory(),
            'debut_pointage' => $debutPointage,
            'fin_pointage' => $finPointage,
            'debut_latitude' => $latitude,
            'debut_longitude' => $longitude,
            'fin_latitude' => $finPointage ? $latitude + $this->faker->randomFloat(6, -0.001, 0.001) : null,
            'fin_longitude' => $finPointage ? $longitude + $this->faker->randomFloat(6, -0.001, 0.001) : null,
            'duree' => $finPointage ? $this->calculateDuration($debutPointage, $finPointage) : null,
            'created_at' => $debutPointage,
            'updated_at' => $finPointage ?? $debutPointage,
        ];
    }

    /**
     * Pointage actif (non terminé)
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'fin_pointage' => null,
            'fin_latitude' => null,
            'fin_longitude' => null,
            'duree' => null,
        ]);
    }

    /**
     * Pointage terminé
     */
    public function completed(): static
    {
        return $this->state(function (array $attributes) {
            $debut = Carbon::instance($attributes['debut_pointage']);
            $fin = $debut->copy()->addHours($this->faker->numberBetween(1, 12));
            
            return [
                'fin_pointage' => $fin,
                'fin_latitude' => $attributes['debut_latitude'] + $this->faker->randomFloat(6, -0.001, 0.001),
                'fin_longitude' => $attributes['debut_longitude'] + $this->faker->randomFloat(6, -0.001, 0.001),
                'duree' => $this->calculateDuration($debut, $fin),
                'updated_at' => $fin,
            ];
        });
    }

    /**
     * Pointage d'aujourd'hui
     */
    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'debut_pointage' => Carbon::today()->addHours($this->faker->numberBetween(7, 10)),
            'created_at' => Carbon::today()->addHours($this->faker->numberBetween(7, 10)),
        ]);
    }

    /**
     * Pointage de cette semaine
     */
    public function thisWeek(): static
    {
        return $this->state(fn (array $attributes) => [
            'debut_pointage' => Carbon::now()->startOfWeek()->addDays($this->faker->numberBetween(0, 6)),
        ]);
    }

    /**
     * Pointage de ce mois
     */
    public function thisMonth(): static
    {
        return $this->state(fn (array $attributes) => [
            'debut_pointage' => Carbon::now()->startOfMonth()->addDays($this->faker->numberBetween(0, 29)),
        ]);
    }

    /**
     * Pointage avec durée spécifique
     */
    public function withDuration(int $hours, int $minutes = 0): static
    {
        return $this->state(function (array $attributes) use ($hours, $minutes) {
            $debut = Carbon::instance($attributes['debut_pointage']);
            $fin = $debut->copy()->addHours($hours)->addMinutes($minutes);
            
            return [
                'fin_pointage' => $fin,
                'duree' => sprintf('%02d:%02d:00', $hours, $minutes),
                'updated_at' => $fin,
            ];
        });
    }

    /**
     * Pointage avec coordonnées spécifiques
     */
    public function withCoordinates(float $lat, float $lon): static
    {
        return $this->state(fn (array $attributes) => [
            'debut_latitude' => $lat,
            'debut_longitude' => $lon,
            'fin_latitude' => $lat + $this->faker->randomFloat(6, -0.0001, 0.0001),
            'fin_longitude' => $lon + $this->faker->randomFloat(6, -0.0001, 0.0001),
        ]);
    }

    /**
     * Pointage pour un utilisateur spécifique
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Pointage pour un site spécifique
     */
    public function forSite(Site $site): static
    {
        return $this->state(fn (array $attributes) => [
            'site_id' => $site->id,
            'debut_latitude' => $site->latitude + $this->faker->randomFloat(6, -0.0001, 0.0001),
            'debut_longitude' => $site->longitude + $this->faker->randomFloat(6, -0.0001, 0.0001),
        ]);
    }

    /**
     * Pointage oublié (commencé il y a longtemps sans fin)
     */
    public function forgotten(): static
    {
        return $this->state(fn (array $attributes) => [
            'debut_pointage' => Carbon::now()->subHours($this->faker->numberBetween(13, 48)),
            'fin_pointage' => null,
            'fin_latitude' => null,
            'fin_longitude' => null,
            'duree' => null,
        ]);
    }

    /**
     * Pointage de nuit
     */
    public function nightShift(): static
    {
        return $this->state(function (array $attributes) {
            $debut = Carbon::yesterday()->setHour(22);
            $fin = Carbon::today()->setHour(6);
            
            return [
                'debut_pointage' => $debut,
                'fin_pointage' => $fin,
                'duree' => $this->calculateDuration($debut, $fin),
                'created_at' => $debut,
                'updated_at' => $fin,
            ];
        });
    }

    /**
     * Pointage de jour normal
     */
    public function dayShift(): static
    {
        return $this->state(function (array $attributes) {
            $debut = Carbon::today()->setHour(8);
            $fin = Carbon::today()->setHour(17);
            
            return [
                'debut_pointage' => $debut,
                'fin_pointage' => $fin,
                'duree' => $this->calculateDuration($debut, $fin),
                'created_at' => $debut,
                'updated_at' => $fin,
            ];
        });
    }

    /**
     * Calcule la durée entre deux dates
     */
    private function calculateDuration($start, $end): string
    {
        if (!$end) {
            return null;
        }

        $startCarbon = Carbon::instance($start);
        $endCarbon = Carbon::instance($end);
        
        if ($endCarbon->lessThan($startCarbon)) {
            return '00:00:00';
        }

        $diff = $endCarbon->diff($startCarbon);
        $totalHours = $diff->days * 24 + $diff->h;
        
        return sprintf('%02d:%02d:%02d', $totalHours, $diff->i, $diff->s);
    }
}
