<?php

namespace Tests\Unit\Services;

use App\Services\LocationService;
use PHPUnit\Framework\TestCase;

/**
 * Tests unitaires pour le LocationService
 */
class LocationServiceTest extends TestCase
{
    private LocationService $locationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->locationService = new LocationService();
    }

    /**
     * Test du calcul de distance avec des coordonnées valides
     */
    public function test_calculate_distance_with_valid_coordinates(): void
    {
        // Coordonnées de test : Paris et Lyon (environ 392 km)
        $parisLat = 48.8566;
        $parisLon = 2.3522;
        $lyonLat = 45.7640;
        $lyonLon = 4.8357;

        $distance = $this->locationService->calculateDistance(
            $parisLat, $parisLon, $lyonLat, $lyonLon
        );

        // La distance devrait être d'environ 392 000 mètres (±5%)
        $this->assertGreaterThan(370000, $distance);
        $this->assertLessThan(415000, $distance);
    }

    /**
     * Test du calcul de distance avec des coordonnées identiques
     */
    public function test_calculate_distance_with_same_coordinates(): void
    {
        $lat = 33.5731;
        $lon = -7.5898; // Casablanca

        $distance = $this->locationService->calculateDistance($lat, $lon, $lat, $lon);

        $this->assertEquals(0, $distance, 'La distance entre des coordonnées identiques devrait être 0');
    }

    /**
     * Test de validation des coordonnées invalides
     */
    public function test_calculate_distance_with_invalid_latitude(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Latitude invalide');

        $this->locationService->calculateDistance(91, 0, 0, 0);
    }

    /**
     * Test de validation des coordonnées invalides (longitude)
     */
    public function test_calculate_distance_with_invalid_longitude(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Longitude invalide');

        $this->locationService->calculateDistance(0, 181, 0, 0);
    }

    /**
     * Test de la vérification de proximité
     */
    public function test_is_within_range_true(): void
    {
        // Coordonnées très proches (environ 30 mètres)
        $lat1 = 33.5731;
        $lon1 = -7.5898;
        $lat2 = 33.5734;
        $lon2 = -7.5901;

        $isWithin = $this->locationService->isWithinRange($lat1, $lon1, $lat2, $lon2, 50);

        $this->assertTrue($isWithin, 'Les coordonnées devraient être dans la zone autorisée');
    }

    /**
     * Test de la vérification de proximité (hors zone)
     */
    public function test_is_within_range_false(): void
    {
        // Coordonnées éloignées
        $casablancaLat = 33.5731;
        $casablancaLon = -7.5898;
        $rabatLat = 34.0209;
        $rabatLon = -6.8416;

        $isWithin = $this->locationService->isWithinRange(
            $casablancaLat, $casablancaLon, $rabatLat, $rabatLon, 50
        );

        $this->assertFalse($isWithin, 'Les coordonnées devraient être hors de la zone autorisée');
    }

    /**
     * Test des informations de distance complètes
     */
    public function test_get_distance_info(): void
    {
        $lat1 = 33.5731;
        $lon1 = -7.5898;
        $lat2 = 33.5734;
        $lon2 = -7.5901;

        $info = $this->locationService->getDistanceInfo($lat1, $lon1, $lat2, $lon2, 100);

        $this->assertIsArray($info);
        $this->assertArrayHasKey('distance', $info);
        $this->assertArrayHasKey('within_range', $info);
        $this->assertArrayHasKey('max_distance', $info);
        $this->assertArrayHasKey('coordinates', $info);

        $this->assertIsFloat($info['distance']);
        $this->assertIsBool($info['within_range']);
        $this->assertEquals(100, $info['max_distance']);
    }

    /**
     * Test de conversion de distance
     */
    public function test_convert_distance(): void
    {
        $distanceInMeters = 1000; // 1 km

        $converted = $this->locationService->convertDistance($distanceInMeters);

        $this->assertIsArray($converted);
        $this->assertEquals(1000, $converted['meters']);
        $this->assertEquals(1, $converted['kilometers']);
        $this->assertGreaterThan(3000, $converted['feet']); // Environ 3280 pieds
        $this->assertLessThan(1, $converted['miles']); // Environ 0.62 miles
    }

    /**
     * Test du calcul de point central
     */
    public function test_calculate_center_point(): void
    {
        $coordinates = [
            ['lat' => 33.5731, 'lon' => -7.5898], // Casablanca
            ['lat' => 34.0209, 'lon' => -6.8416], // Rabat
            ['lat' => 31.6295, 'lon' => -7.9811], // Marrakech
        ];

        $center = $this->locationService->calculateCenterPoint($coordinates);

        $this->assertIsArray($center);
        $this->assertArrayHasKey('lat', $center);
        $this->assertArrayHasKey('lon', $center);

        // Le centre devrait être quelque part au Maroc
        $this->assertGreaterThan(30, $center['lat']);
        $this->assertLessThan(35, $center['lat']);
        $this->assertGreaterThan(-10, $center['lon']);
        $this->assertLessThan(-5, $center['lon']);
    }

    /**
     * Test du calcul de point central avec coordonnées vides
     */
    public function test_calculate_center_point_empty_coordinates(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Au moins une coordonnée est requise');

        $this->locationService->calculateCenterPoint([]);
    }

    /**
     * Test de génération de périmètre circulaire
     */
    public function test_generate_circle_perimeter(): void
    {
        $centerLat = 33.5731;
        $centerLon = -7.5898;
        $radius = 100; // 100 mètres
        $points = 8;

        $perimeter = $this->locationService->generateCirclePerimeter(
            $centerLat, $centerLon, $radius, $points
        );

        $this->assertIsArray($perimeter);
        $this->assertCount($points, $perimeter);

        foreach ($perimeter as $point) {
            $this->assertArrayHasKey('lat', $point);
            $this->assertArrayHasKey('lon', $point);
            $this->assertIsFloat($point['lat']);
            $this->assertIsFloat($point['lon']);

            // Vérifier que le point est approximativement à la bonne distance
            $distance = $this->locationService->calculateDistance(
                $centerLat, $centerLon, $point['lat'], $point['lon']
            );
            
            // Tolérance de ±20% pour l'approximation
            $this->assertGreaterThan($radius * 0.8, $distance);
            $this->assertLessThan($radius * 1.2, $distance);
        }
    }

    /**
     * Test de performance pour de nombreux calculs
     */
    public function test_performance_multiple_calculations(): void
    {
        $startTime = microtime(true);

        // Effectuer 1000 calculs de distance
        for ($i = 0; $i < 1000; $i++) {
            $this->locationService->calculateDistance(
                33.5731 + ($i * 0.001),
                -7.5898 + ($i * 0.001),
                33.5731,
                -7.5898
            );
        }

        $endTime = microtime(true);
        $duration = $endTime - $startTime;

        // Le calcul de 1000 distances devrait prendre moins de 1 seconde
        $this->assertLessThan(1.0, $duration, 'Les calculs de distance devraient être rapides');
    }

    /**
     * Test de précision avec des coordonnées très proches
     */
    public function test_precision_with_close_coordinates(): void
    {
        $lat1 = 33.573100;
        $lon1 = -7.589800;
        $lat2 = 33.573101; // Différence de 0.000001 degré
        $lon2 = -7.589801;

        $distance = $this->locationService->calculateDistance($lat1, $lon1, $lat2, $lon2);

        // La distance devrait être très petite mais non nulle
        $this->assertGreaterThan(0, $distance);
        $this->assertLessThan(1, $distance); // Moins d'un mètre
    }
}
