<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Supprimer les colonnes qui ne sont pas dans la structure originale
            $table->dropColumn(['email_verified_at', 'remember_token']);

            // Ajouter l'index sur email si pas déjà présent
            $table->index('email', 'idx_users_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Restaurer les colonnes supprimées
            $table->timestamp('email_verified_at')->nullable();
            $table->rememberToken();

            // Supprimer l'index
            $table->dropIndex('idx_users_email');
        });
    }
};
