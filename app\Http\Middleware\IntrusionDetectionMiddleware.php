<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Middleware de détection d'intrusion
 * Surveille et bloque les activités suspectes
 */
class IntrusionDetectionMiddleware
{
    /**
     * Seuils de détection
     */
    private const THRESHOLDS = [
        'requests_per_minute' => 100,
        'failed_attempts_per_hour' => 10,
        'suspicious_patterns_per_day' => 5,
    ];

    /**
     * Durées de blocage (en minutes)
     */
    private const BLOCK_DURATIONS = [
        'suspicious_activity' => 60,
        'attack_pattern' => 240,
        'repeated_violations' => 1440, // 24 heures
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $clientId = $this->getClientIdentifier($request);

        // Vérifier si le client est bloqué
        if ($this->isBlocked($clientId)) {
            return $this->blockedResponse($clientId);
        }

        // Détecter les patterns d'attaque
        $attackPatterns = SecurityHeadersMiddleware::detectAttackPatterns($request);
        if (!empty($attackPatterns)) {
            $this->handleAttackDetection($request, $clientId, $attackPatterns);
            return $this->attackDetectedResponse();
        }

        // Surveiller l'activité
        $this->monitorActivity($request, $clientId);

        $response = $next($request);

        // Analyser la réponse pour détecter des anomalies
        $this->analyzeResponse($request, $response, $clientId);

        return $response;
    }

    /**
     * Génère un identifiant unique pour le client
     */
    private function getClientIdentifier(Request $request): string
    {
        // Combinaison IP + User-Agent pour identifier le client
        $ip = $request->ip();
        $userAgent = $request->header('User-Agent', 'unknown');
        
        return 'client:' . md5($ip . '|' . $userAgent);
    }

    /**
     * Vérifie si un client est bloqué
     */
    private function isBlocked(string $clientId): bool
    {
        return Cache::has("blocked:{$clientId}");
    }

    /**
     * Surveille l'activité du client
     */
    private function monitorActivity(Request $request, string $clientId): void
    {
        $now = now();
        $minute = $now->format('Y-m-d H:i');
        
        // Compter les requêtes par minute
        $requestKey = "requests:{$clientId}:{$minute}";
        $requestCount = Cache::increment($requestKey);
        
        if ($requestCount === 1) {
            Cache::put($requestKey, 1, 120); // Expire après 2 minutes
        }

        // Vérifier le seuil de requêtes
        if ($requestCount > self::THRESHOLDS['requests_per_minute']) {
            $this->blockClient($clientId, 'excessive_requests', self::BLOCK_DURATIONS['suspicious_activity']);
            
            Log::channel('security')->warning('Activité excessive détectée', [
                'client_id' => $clientId,
                'ip' => $request->ip(),
                'requests_count' => $requestCount,
                'user_agent' => $request->header('User-Agent'),
                'endpoint' => $request->path(),
            ]);
        }

        // Surveiller les tentatives de connexion échouées
        if ($request->is('api/auth/login')) {
            $this->monitorLoginAttempts($request, $clientId);
        }
    }

    /**
     * Surveille les tentatives de connexion
     */
    private function monitorLoginAttempts(Request $request, string $clientId): void
    {
        $hour = now()->format('Y-m-d H');
        $failedKey = "failed_logins:{$clientId}:{$hour}";
        
        // Cette méthode sera appelée après la réponse pour vérifier le succès/échec
        // Pour l'instant, on prépare juste la surveillance
    }

    /**
     * Gère la détection d'attaque
     */
    private function handleAttackDetection(Request $request, string $clientId, array $patterns): void
    {
        // Bloquer immédiatement
        $this->blockClient($clientId, 'attack_pattern', self::BLOCK_DURATIONS['attack_pattern']);

        // Incrémenter le compteur de patterns suspects
        $day = now()->format('Y-m-d');
        $patternKey = "suspicious_patterns:{$clientId}:{$day}";
        $patternCount = Cache::increment($patternKey);
        
        if ($patternCount === 1) {
            Cache::put($patternKey, 1, 1440); // Expire après 24 heures
        }

        // Si trop de patterns suspects, bloquer plus longtemps
        if ($patternCount >= self::THRESHOLDS['suspicious_patterns_per_day']) {
            $this->blockClient($clientId, 'repeated_violations', self::BLOCK_DURATIONS['repeated_violations']);
        }

        // Log détaillé de l'attaque
        Log::channel('security')->critical('Tentative d\'attaque détectée', [
            'client_id' => $clientId,
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'endpoint' => $request->path(),
            'method' => $request->method(),
            'patterns' => $patterns,
            'payload' => $request->except(['password', 'password_confirmation']),
            'headers' => $request->headers->all(),
        ]);
    }

    /**
     * Analyse la réponse pour détecter des anomalies
     */
    private function analyzeResponse(Request $request, Response $response, string $clientId): void
    {
        $statusCode = $response->getStatusCode();

        // Surveiller les erreurs 401/403 (tentatives d'accès non autorisé)
        if (in_array($statusCode, [401, 403])) {
            $this->recordUnauthorizedAttempt($request, $clientId);
        }

        // Surveiller les erreurs 404 (scanning de endpoints)
        if ($statusCode === 404) {
            $this->recordNotFoundAttempt($request, $clientId);
        }

        // Surveiller les erreurs 422 (tentatives de validation)
        if ($statusCode === 422) {
            $this->recordValidationFailure($request, $clientId);
        }
    }

    /**
     * Enregistre une tentative d'accès non autorisé
     */
    private function recordUnauthorizedAttempt(Request $request, string $clientId): void
    {
        $hour = now()->format('Y-m-d H');
        $key = "unauthorized:{$clientId}:{$hour}";
        $count = Cache::increment($key);
        
        if ($count === 1) {
            Cache::put($key, 1, 120);
        }

        if ($count >= 20) { // 20 tentatives non autorisées par heure
            $this->blockClient($clientId, 'unauthorized_attempts', self::BLOCK_DURATIONS['suspicious_activity']);
            
            Log::channel('security')->warning('Tentatives d\'accès non autorisé répétées', [
                'client_id' => $clientId,
                'ip' => $request->ip(),
                'count' => $count,
                'endpoint' => $request->path(),
            ]);
        }
    }

    /**
     * Enregistre une tentative de scanning (404)
     */
    private function recordNotFoundAttempt(Request $request, string $clientId): void
    {
        $hour = now()->format('Y-m-d H');
        $key = "not_found:{$clientId}:{$hour}";
        $count = Cache::increment($key);
        
        if ($count === 1) {
            Cache::put($key, 1, 120);
        }

        if ($count >= 50) { // 50 endpoints inexistants par heure
            $this->blockClient($clientId, 'endpoint_scanning', self::BLOCK_DURATIONS['suspicious_activity']);
            
            Log::channel('security')->warning('Scanning d\'endpoints détecté', [
                'client_id' => $clientId,
                'ip' => $request->ip(),
                'count' => $count,
                'last_endpoint' => $request->path(),
            ]);
        }
    }

    /**
     * Enregistre un échec de validation
     */
    private function recordValidationFailure(Request $request, string $clientId): void
    {
        $minute = now()->format('Y-m-d H:i');
        $key = "validation_failures:{$clientId}:{$minute}";
        $count = Cache::increment($key);
        
        if ($count === 1) {
            Cache::put($key, 1, 120);
        }

        if ($count >= 10) { // 10 échecs de validation par minute
            $this->blockClient($clientId, 'validation_abuse', self::BLOCK_DURATIONS['suspicious_activity']);
        }
    }

    /**
     * Bloque un client
     */
    private function blockClient(string $clientId, string $reason, int $duration): void
    {
        $blockKey = "blocked:{$clientId}";
        $blockData = [
            'reason' => $reason,
            'blocked_at' => now()->toISOString(),
            'duration' => $duration,
        ];
        
        Cache::put($blockKey, $blockData, $duration);
        
        // Enregistrer dans les logs
        Log::channel('security')->error('Client bloqué', [
            'client_id' => $clientId,
            'reason' => $reason,
            'duration' => $duration,
            'blocked_until' => now()->addMinutes($duration)->toISOString(),
        ]);
    }

    /**
     * Réponse pour client bloqué
     */
    private function blockedResponse(string $clientId): Response
    {
        $blockData = Cache::get("blocked:{$clientId}");
        
        return response()->json([
            'success' => false,
            'message' => 'Accès temporairement bloqué en raison d\'activité suspecte.',
            'message_ar' => 'تم حظر الوصول مؤقتاً بسبب نشاط مشبوه.',
            'reason' => 'security_block',
            'blocked_at' => $blockData['blocked_at'] ?? null,
        ], 429);
    }

    /**
     * Réponse pour attaque détectée
     */
    private function attackDetectedResponse(): Response
    {
        return response()->json([
            'success' => false,
            'message' => 'Requête rejetée pour des raisons de sécurité.',
            'message_ar' => 'تم رفض الطلب لأسباب أمنية.',
            'reason' => 'security_violation',
        ], 403);
    }

    /**
     * Obtient les statistiques de sécurité
     */
    public static function getSecurityStats(): array
    {
        // Cette méthode pourrait être utilisée par un dashboard admin
        return [
            'blocked_clients' => 0, // À implémenter
            'attack_attempts_today' => 0, // À implémenter
            'suspicious_activities' => 0, // À implémenter
        ];
    }
}
