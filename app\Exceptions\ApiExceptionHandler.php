<?php

namespace App\Exceptions;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;

/**
 * Gestionnaire d'exceptions API professionnel
 * Fournit des réponses JSON cohérentes et sécurisées
 */
class ApiExceptionHandler
{
    /**
     * Gère les exceptions pour les routes API
     */
    public static function handle(\Throwable $exception, Request $request): JsonResponse
    {
        // Log l'exception avec contexte
        self::logException($exception, $request);

        // Traitement spécialisé par type d'exception
        return match (true) {
            $exception instanceof ValidationException => self::handleValidationException($exception),
            $exception instanceof AuthenticationException => self::handleAuthenticationException($exception),
            $exception instanceof AuthorizationException => self::handleAuthorizationException($exception),
            $exception instanceof ModelNotFoundException => self::handleModelNotFoundException($exception),
            $exception instanceof NotFoundHttpException => self::handleNotFoundHttpException($exception),
            $exception instanceof MethodNotAllowedHttpException => self::handleMethodNotAllowedException($exception),
            $exception instanceof HttpException => self::handleHttpException($exception),
            default => self::handleGenericException($exception)
        };
    }

    /**
     * Gère les erreurs de validation
     */
    private static function handleValidationException(ValidationException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Données de validation invalides.',
            'message_ar' => 'بيانات التحقق غير صالحة.',
            'errors' => $exception->errors(),
            'error_code' => 'VALIDATION_ERROR'
        ], 422);
    }

    /**
     * Gère les erreurs d'authentification
     */
    private static function handleAuthenticationException(AuthenticationException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Authentification requise.',
            'message_ar' => 'المصادقة مطلوبة.',
            'error_code' => 'AUTHENTICATION_REQUIRED'
        ], 401);
    }

    /**
     * Gère les erreurs d'autorisation
     */
    private static function handleAuthorizationException(AuthorizationException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Accès non autorisé.',
            'message_ar' => 'الوصول غير مصرح به.',
            'error_code' => 'ACCESS_DENIED'
        ], 403);
    }

    /**
     * Gère les erreurs de modèle non trouvé
     */
    private static function handleModelNotFoundException(ModelNotFoundException $exception): JsonResponse
    {
        $model = class_basename($exception->getModel());
        
        return response()->json([
            'success' => false,
            'message' => "Ressource {$model} non trouvée.",
            'message_ar' => "المورد {$model} غير موجود.",
            'error_code' => 'RESOURCE_NOT_FOUND'
        ], 404);
    }

    /**
     * Gère les erreurs 404
     */
    private static function handleNotFoundHttpException(NotFoundHttpException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Endpoint non trouvé.',
            'message_ar' => 'نقطة النهاية غير موجودة.',
            'error_code' => 'ENDPOINT_NOT_FOUND'
        ], 404);
    }

    /**
     * Gère les erreurs de méthode non autorisée
     */
    private static function handleMethodNotAllowedException(MethodNotAllowedHttpException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Méthode HTTP non autorisée.',
            'message_ar' => 'طريقة HTTP غير مسموحة.',
            'error_code' => 'METHOD_NOT_ALLOWED',
            'allowed_methods' => $exception->getHeaders()['Allow'] ?? []
        ], 405);
    }

    /**
     * Gère les exceptions HTTP génériques
     */
    private static function handleHttpException(HttpException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $exception->getMessage() ?: 'Erreur HTTP.',
            'message_ar' => 'خطأ HTTP.',
            'error_code' => 'HTTP_ERROR'
        ], $exception->getStatusCode());
    }

    /**
     * Gère les exceptions génériques
     */
    private static function handleGenericException(\Throwable $exception): JsonResponse
    {
        $isDebug = config('app.debug');
        
        $response = [
            'success' => false,
            'message' => 'Une erreur interne s\'est produite.',
            'message_ar' => 'حدث خطأ داخلي.',
            'error_code' => 'INTERNAL_ERROR'
        ];

        // En mode debug, ajouter les détails de l'exception
        if ($isDebug) {
            $response['debug'] = [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ];
        }

        return response()->json($response, 500);
    }

    /**
     * Log l'exception avec contexte
     */
    private static function logException(\Throwable $exception, Request $request): void
    {
        $context = [
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => $request->user()?->id,
            'request_data' => $request->except(['password', 'password_confirmation'])
        ];

        // Log selon la sévérité
        if ($exception instanceof ValidationException || 
            $exception instanceof AuthenticationException ||
            $exception instanceof AuthorizationException) {
            Log::channel('api')->info('API Exception', $context);
        } elseif ($exception instanceof NotFoundHttpException ||
                  $exception instanceof ModelNotFoundException) {
            Log::channel('api')->notice('API Not Found', $context);
        } else {
            Log::channel('api')->error('API Error', $context);
        }
    }

    /**
     * Détermine si l'exception doit être reportée
     */
    public static function shouldReport(\Throwable $exception): bool
    {
        // Ne pas reporter les exceptions de validation et d'authentification
        return !($exception instanceof ValidationException ||
                 $exception instanceof AuthenticationException ||
                 $exception instanceof AuthorizationException ||
                 $exception instanceof NotFoundHttpException ||
                 $exception instanceof ModelNotFoundException);
    }
}
