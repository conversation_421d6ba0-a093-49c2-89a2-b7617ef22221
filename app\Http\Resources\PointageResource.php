<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PointageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user' => new UserResource($this->whenLoaded('user')),
            'site' => new SiteResource($this->whenLoaded('site')),
            'debut_pointage' => $this->debut_pointage?->format('Y-m-d H:i:s'),
            'fin_pointage' => $this->fin_pointage?->format('Y-m-d H:i:s'),
            'duree' => $this->duree,
            'debut_latitude' => $this->debut_latitude,
            'debut_longitude' => $this->debut_longitude,
            'fin_latitude' => $this->fin_latitude,
            'fin_longitude' => $this->fin_longitude,
            'is_active' => $this->isActive(),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
