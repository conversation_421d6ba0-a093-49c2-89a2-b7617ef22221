<?php

namespace App\Services;

use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Service de cache optimisé pour les données fréquemment accédées
 * Améliore les performances en réduisant les requêtes à la base de données
 */
class CacheService
{
    /**
     * Durées de cache par défaut (en secondes)
     */
    private const CACHE_DURATIONS = [
        'user_sites' => 1800,        // 30 minutes
        'active_pointages' => 300,   // 5 minutes
        'site_users' => 1800,        // 30 minutes
        'user_stats' => 3600,        // 1 heure
        'dashboard_data' => 600,     // 10 minutes
    ];

    /**
     * Récupère les sites assignés à un utilisateur (avec cache)
     */
    public function getUserSites(int $userId): \Illuminate\Support\Collection
    {
        $cacheKey = "user_sites:{$userId}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['user_sites'], function () use ($userId) {
            return DB::table('assignments')
                ->join('sites', 'assignments.site_id', '=', 'sites.id')
                ->where('assignments.user_id', $userId)
                ->select('sites.*')
                ->get();
        });
    }

    /**
     * Récupère le site assigné principal d'un utilisateur
     */
    public function getUserPrimarySite(int $userId): ?Site
    {
        $cacheKey = "user_primary_site:{$userId}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['user_sites'], function () use ($userId) {
            $assignment = DB::table('assignments')
                ->where('user_id', $userId)
                ->first();
                
            return $assignment ? Site::find($assignment->site_id) : null;
        });
    }

    /**
     * Récupère les pointages actifs d'un utilisateur
     */
    public function getUserActivePointages(int $userId): \Illuminate\Support\Collection
    {
        $cacheKey = "active_pointages:{$userId}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['active_pointages'], function () use ($userId) {
            return Pointage::forUser($userId)
                ->active()
                ->withOptimizedRelations()
                ->get();
        });
    }

    /**
     * Récupère les utilisateurs assignés à un site
     */
    public function getSiteUsers(int $siteId): \Illuminate\Support\Collection
    {
        $cacheKey = "site_users:{$siteId}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['site_users'], function () use ($siteId) {
            return DB::table('assignments')
                ->join('users', 'assignments.user_id', '=', 'users.id')
                ->where('assignments.site_id', $siteId)
                ->select('users.id', 'users.name', 'users.email', 'users.role')
                ->get();
        });
    }

    /**
     * Récupère les statistiques d'un utilisateur pour une période
     */
    public function getUserStats(int $userId, Carbon $from, Carbon $to): array
    {
        $cacheKey = "user_stats:{$userId}:" . $from->format('Y-m-d') . ':' . $to->format('Y-m-d');
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['user_stats'], function () use ($userId, $from, $to) {
            $pointages = Pointage::forUser($userId)
                ->betweenDates($from, $to)
                ->completed()
                ->get();

            $totalHours = 0;
            $totalDays = $pointages->count();

            foreach ($pointages as $pointage) {
                if ($pointage->duree) {
                    $parts = explode(':', $pointage->duree);
                    $totalHours += (int)$parts[0] + ((int)$parts[1] / 60) + ((int)$parts[2] / 3600);
                }
            }

            return [
                'total_days' => $totalDays,
                'total_hours' => round($totalHours, 2),
                'average_hours_per_day' => $totalDays > 0 ? round($totalHours / $totalDays, 2) : 0,
                'period' => [
                    'from' => $from->format('Y-m-d'),
                    'to' => $to->format('Y-m-d')
                ]
            ];
        });
    }

    /**
     * Récupère les données du tableau de bord admin
     */
    public function getDashboardData(): array
    {
        $cacheKey = 'admin_dashboard_data';
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['dashboard_data'], function () {
            $today = Carbon::today();
            
            return [
                'total_users' => User::employees()->count(),
                'total_sites' => Site::count(),
                'active_pointages_today' => Pointage::today()->active()->count(),
                'completed_pointages_today' => Pointage::today()->completed()->count(),
                'total_pointages_this_month' => Pointage::thisMonth()->count(),
                'users_with_active_pointages' => Pointage::active()
                    ->distinct('user_id')
                    ->count('user_id'),
                'sites_with_active_pointages' => Pointage::active()
                    ->distinct('site_id')
                    ->count('site_id'),
            ];
        });
    }

    /**
     * Récupère la liste des sites avec cache
     */
    public function getAllSites(): \Illuminate\Support\Collection
    {
        $cacheKey = 'all_sites';
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['site_users'], function () {
            return Site::select('id', 'name', 'latitude', 'longitude')
                ->orderBy('name')
                ->get();
        });
    }

    /**
     * Récupère la liste des employés avec cache
     */
    public function getAllEmployees(): \Illuminate\Support\Collection
    {
        $cacheKey = 'all_employees';
        
        return Cache::remember($cacheKey, self::CACHE_DURATIONS['site_users'], function () {
            return User::employees()
                ->select('id', 'name', 'email')
                ->orderBy('name')
                ->get();
        });
    }

    /**
     * Invalide le cache lié à un utilisateur
     */
    public function invalidateUserCache(int $userId): void
    {
        $patterns = [
            "user_sites:{$userId}",
            "user_primary_site:{$userId}",
            "active_pointages:{$userId}",
            "user_stats:{$userId}:*",
        ];

        foreach ($patterns as $pattern) {
            if (str_contains($pattern, '*')) {
                // Pour les patterns avec wildcard, on doit itérer
                $this->forgetByPattern($pattern);
            } else {
                Cache::forget($pattern);
            }
        }

        // Invalider le cache du dashboard
        Cache::forget('admin_dashboard_data');
    }

    /**
     * Invalide le cache lié à un site
     */
    public function invalidateSiteCache(int $siteId): void
    {
        Cache::forget("site_users:{$siteId}");
        Cache::forget('all_sites');
        Cache::forget('admin_dashboard_data');
    }

    /**
     * Invalide tout le cache des pointages
     */
    public function invalidatePointageCache(): void
    {
        // Patterns à invalider
        $patterns = [
            'active_pointages:*',
            'user_stats:*',
            'admin_dashboard_data',
        ];

        foreach ($patterns as $pattern) {
            if (str_contains($pattern, '*')) {
                $this->forgetByPattern($pattern);
            } else {
                Cache::forget($pattern);
            }
        }
    }

    /**
     * Précharge les données critiques
     */
    public function preloadCriticalData(): void
    {
        // Précharger les données du dashboard
        $this->getDashboardData();
        
        // Précharger la liste des sites
        $this->getAllSites();
        
        // Précharger la liste des employés
        $this->getAllEmployees();
    }

    /**
     * Nettoie le cache expiré et optimise
     */
    public function cleanup(): void
    {
        // Cette méthode peut être appelée par une commande artisan
        // pour nettoyer le cache périodiquement
        
        if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
            // Optimisations spécifiques à Redis
            Cache::getStore()->getRedis()->flushdb();
        }
    }

    /**
     * Supprime les clés de cache par pattern (implémentation simple)
     */
    private function forgetByPattern(string $pattern): void
    {
        // Cette implémentation dépend du driver de cache utilisé
        // Pour Redis, on pourrait utiliser SCAN avec MATCH
        // Pour l'instant, implémentation basique
        
        $prefix = str_replace('*', '', $pattern);
        
        // Méthode simplifiée - dans un vrai projet, 
        // il faudrait une implémentation plus robuste
        for ($i = 1; $i <= 1000; $i++) {
            Cache::forget($prefix . $i);
        }
    }

    /**
     * Récupère les métriques de performance du cache
     */
    public function getCacheMetrics(): array
    {
        // Retourne des métriques sur l'utilisation du cache
        return [
            'driver' => config('cache.default'),
            'prefix' => config('cache.prefix'),
            'timestamp' => now()->toISOString(),
        ];
    }
}
