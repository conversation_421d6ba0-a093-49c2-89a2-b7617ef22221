#!/bin/bash

# Script de déploiement et d'optimisation ClockIn API
# Usage: ./deploy.sh [environment] [options]

set -e  # Arrêter en cas d'erreur

# Configuration
PROJECT_NAME="ClockIn API"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="backups"
LOG_FILE="deploy_${TIMESTAMP}.log"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonctions utilitaires
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
    exit 1
}

# Vérifier les prérequis
check_requirements() {
    log "Vérification des prérequis..."
    
    # Vérifier PHP
    if ! command -v php &> /dev/null; then
        error "PHP n'est pas installé"
    fi
    
    # Vérifier Composer
    if ! command -v composer &> /dev/null; then
        error "Composer n'est pas installé"
    fi
    
    # Vérifier Node.js (si nécessaire)
    if ! command -v node &> /dev/null; then
        warning "Node.js n'est pas installé (optionnel)"
    fi
    
    success "Prérequis vérifiés"
}

# Sauvegarder la base de données
backup_database() {
    log "Sauvegarde de la base de données..."
    
    mkdir -p "$BACKUP_DIR"
    
    if [ "$DB_CONNECTION" = "mysql" ]; then
        mysqldump -h"$DB_HOST" -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" > "$BACKUP_DIR/db_backup_${TIMESTAMP}.sql"
        success "Base de données MySQL sauvegardée"
    elif [ "$DB_CONNECTION" = "sqlite" ]; then
        cp "$DB_DATABASE" "$BACKUP_DIR/db_backup_${TIMESTAMP}.sqlite"
        success "Base de données SQLite sauvegardée"
    else
        warning "Type de base de données non supporté pour la sauvegarde"
    fi
}

# Mettre à jour les dépendances
update_dependencies() {
    log "Mise à jour des dépendances..."
    
    # Composer
    composer install --no-dev --optimize-autoloader --no-interaction
    success "Dépendances Composer mises à jour"
    
    # NPM (si package.json existe)
    if [ -f "package.json" ]; then
        npm ci --production
        npm run build
        success "Dépendances NPM mises à jour"
    fi
}

# Exécuter les migrations
run_migrations() {
    log "Exécution des migrations..."
    
    php artisan migrate --force
    success "Migrations exécutées"
}

# Optimiser l'application
optimize_application() {
    log "Optimisation de l'application..."
    
    # Nettoyer le cache
    php artisan cache:clear
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    
    # Optimiser pour la production
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    php artisan event:cache
    
    # Optimiser l'autoloader
    composer dump-autoload --optimize --classmap-authoritative
    
    # Optimiser les performances ClockIn
    php artisan clockin:optimize --all
    
    success "Application optimisée"
}

# Exécuter les tests
run_tests() {
    log "Exécution des tests..."
    
    # Configurer l'environnement de test
    cp .env.testing .env.test.backup 2>/dev/null || true
    
    # Exécuter les tests
    php artisan clockin:test --unit --feature
    
    if [ $? -eq 0 ]; then
        success "Tous les tests sont passés"
    else
        error "Certains tests ont échoué"
    fi
}

# Vérifier la santé de l'application
health_check() {
    log "Vérification de la santé de l'application..."
    
    # Vérifier que l'application répond
    if command -v curl &> /dev/null; then
        response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/api/health 2>/dev/null || echo "000")
        
        if [ "$response" = "200" ]; then
            success "Application accessible"
        else
            warning "Application non accessible (code: $response)"
        fi
    else
        warning "curl non disponible pour le health check"
    fi
    
    # Vérifier les permissions
    if [ -w "storage" ] && [ -w "bootstrap/cache" ]; then
        success "Permissions correctes"
    else
        error "Problème de permissions sur storage/ ou bootstrap/cache/"
    fi
}

# Configurer les permissions
set_permissions() {
    log "Configuration des permissions..."
    
    # Permissions pour Laravel
    chmod -R 755 storage bootstrap/cache
    chown -R www-data:www-data storage bootstrap/cache 2>/dev/null || true
    
    success "Permissions configurées"
}

# Redémarrer les services
restart_services() {
    log "Redémarrage des services..."
    
    # PHP-FPM
    if command -v systemctl &> /dev/null; then
        systemctl reload php8.1-fpm 2>/dev/null || systemctl reload php-fpm 2>/dev/null || true
        success "PHP-FPM redémarré"
    fi
    
    # Nginx
    if command -v nginx &> /dev/null; then
        nginx -t && nginx -s reload 2>/dev/null || true
        success "Nginx redémarré"
    fi
    
    # Queue workers (si utilisés)
    if [ -f "artisan" ]; then
        php artisan queue:restart 2>/dev/null || true
        success "Queue workers redémarrés"
    fi
}

# Afficher le résumé
show_summary() {
    log "Résumé du déploiement"
    echo "=================================="
    echo "Projet: $PROJECT_NAME"
    echo "Timestamp: $TIMESTAMP"
    echo "Environnement: ${ENVIRONMENT:-production}"
    echo "Log: $LOG_FILE"
    echo "=================================="
    success "Déploiement terminé avec succès!"
}

# Fonction principale
main() {
    local environment=${1:-production}
    local skip_tests=${2:-false}
    
    echo "🚀 Déploiement de $PROJECT_NAME"
    echo "Environnement: $environment"
    echo "=================================="
    
    # Charger les variables d'environnement
    if [ -f ".env.$environment" ]; then
        source ".env.$environment"
        log "Variables d'environnement chargées depuis .env.$environment"
    elif [ -f ".env" ]; then
        source ".env"
        log "Variables d'environnement chargées depuis .env"
    else
        warning "Aucun fichier d'environnement trouvé"
    fi
    
    # Exécuter les étapes de déploiement
    check_requirements
    
    if [ "$environment" = "production" ]; then
        backup_database
    fi
    
    update_dependencies
    run_migrations
    optimize_application
    
    if [ "$skip_tests" != "true" ] && [ "$environment" != "production" ]; then
        run_tests
    fi
    
    set_permissions
    
    if [ "$environment" = "production" ]; then
        restart_services
    fi
    
    health_check
    show_summary
}

# Gestion des arguments
case "${1:-}" in
    "production"|"staging"|"development")
        main "$1" "$2"
        ;;
    "--help"|"-h")
        echo "Usage: $0 [environment] [skip-tests]"
        echo "Environments: production, staging, development"
        echo "Options:"
        echo "  skip-tests: Ignorer l'exécution des tests"
        echo ""
        echo "Exemples:"
        echo "  $0 production"
        echo "  $0 staging skip-tests"
        echo "  $0 development"
        exit 0
        ;;
    *)
        warning "Environnement non spécifié, utilisation de 'production'"
        main "production" "$2"
        ;;
esac
