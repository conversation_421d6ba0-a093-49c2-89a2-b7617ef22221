<?php

namespace Tests\Unit\Services;

use App\Models\Pointage;
use App\Models\Site;
use App\Models\User;
use App\Services\LocationService;
use App\Services\PointageService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

/**
 * Tests unitaires pour le PointageService
 */
class PointageServiceTest extends TestCase
{
    use RefreshDatabase;

    private PointageService $pointageService;
    private LocationService $locationService;
    private User $user;
    private Site $site;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->locationService = new LocationService();
        $this->pointageService = new PointageService($this->locationService);

        // Créer des données de test
        $this->user = User::factory()->create([
            'role' => 'employee'
        ]);

        $this->site = Site::factory()->create([
            'latitude' => 33.5731,
            'longitude' => -7.5898,
        ]);
    }

    /**
     * Test de démarrage d'un nouveau pointage
     */
    public function test_start_pointage(): void
    {
        $latitude = 33.5732;
        $longitude = -7.5899;

        $result = $this->pointageService->startPointage(
            $this->user,
            $this->site,
            $latitude,
            $longitude
        );

        $this->assertEquals('start', $result['type']);
        $this->assertArrayHasKey('pointage', $result);
        $this->assertInstanceOf(Pointage::class, $result['pointage']);

        $pointage = $result['pointage'];
        $this->assertEquals($this->user->id, $pointage->user_id);
        $this->assertEquals($this->site->id, $pointage->site_id);
        $this->assertEquals($latitude, $pointage->debut_latitude);
        $this->assertEquals($longitude, $pointage->debut_longitude);
        $this->assertNotNull($pointage->debut_pointage);
        $this->assertNull($pointage->fin_pointage);
    }

    /**
     * Test de fin d'un pointage existant
     */
    public function test_end_pointage(): void
    {
        // Créer un pointage actif
        $pointage = Pointage::factory()->create([
            'user_id' => $this->user->id,
            'site_id' => $this->site->id,
            'debut_pointage' => Carbon::now()->subHours(2),
            'debut_latitude' => 33.5731,
            'debut_longitude' => -7.5898,
            'fin_pointage' => null,
        ]);

        $endLatitude = 33.5733;
        $endLongitude = -7.5897;

        $result = $this->pointageService->endPointage($pointage, $endLatitude, $endLongitude);

        $this->assertEquals('end', $result['type']);
        $this->assertArrayHasKey('pointage', $result);

        $updatedPointage = $result['pointage'];
        $this->assertNotNull($updatedPointage->fin_pointage);
        $this->assertEquals($endLatitude, $updatedPointage->fin_latitude);
        $this->assertEquals($endLongitude, $updatedPointage->fin_longitude);
        $this->assertNotNull($updatedPointage->duree);
    }

    /**
     * Test du traitement d'un pointage (début)
     */
    public function test_process_pointage_start(): void
    {
        $latitude = 33.5732;
        $longitude = -7.5899;

        $result = $this->pointageService->processPointage(
            $this->user,
            $this->site,
            $latitude,
            $longitude
        );

        $this->assertEquals('start', $result['type']);
        $this->assertDatabaseHas('pointages', [
            'user_id' => $this->user->id,
            'site_id' => $this->site->id,
            'debut_latitude' => $latitude,
            'debut_longitude' => $longitude,
        ]);
    }

    /**
     * Test du traitement d'un pointage (fin)
     */
    public function test_process_pointage_end(): void
    {
        // Créer un pointage actif
        Pointage::factory()->create([
            'user_id' => $this->user->id,
            'site_id' => $this->site->id,
            'debut_pointage' => Carbon::now()->subHours(1),
            'fin_pointage' => null,
        ]);

        $latitude = 33.5733;
        $longitude = -7.5897;

        $result = $this->pointageService->processPointage(
            $this->user,
            $this->site,
            $latitude,
            $longitude
        );

        $this->assertEquals('end', $result['type']);
    }

    /**
     * Test de récupération du pointage actif
     */
    public function test_get_active_pointage(): void
    {
        // Créer un pointage actif
        $activePointage = Pointage::factory()->create([
            'user_id' => $this->user->id,
            'site_id' => $this->site->id,
            'fin_pointage' => null,
        ]);

        // Créer un pointage terminé
        Pointage::factory()->create([
            'user_id' => $this->user->id,
            'site_id' => $this->site->id,
            'fin_pointage' => Carbon::now(),
        ]);

        $result = $this->pointageService->getActivePointage($this->user->id);

        $this->assertNotNull($result);
        $this->assertEquals($activePointage->id, $result->id);
    }

    /**
     * Test de récupération du pointage actif (aucun)
     */
    public function test_get_active_pointage_none(): void
    {
        $result = $this->pointageService->getActivePointage($this->user->id);

        $this->assertNull($result);
    }

    /**
     * Test de calcul de durée
     */
    public function test_calculate_duration(): void
    {
        $start = Carbon::parse('2024-01-01 09:00:00');
        $end = Carbon::parse('2024-01-01 17:30:00');

        $duration = $this->pointageService->calculateDuration($start, $end);

        $this->assertEquals('08:30:00', $duration);
    }

    /**
     * Test de calcul de durée avec fin antérieure au début
     */
    public function test_calculate_duration_invalid(): void
    {
        $start = Carbon::parse('2024-01-01 17:00:00');
        $end = Carbon::parse('2024-01-01 09:00:00');

        $duration = $this->pointageService->calculateDuration($start, $end);

        $this->assertEquals('00:00:00', $duration);
    }

    /**
     * Test de calcul de durée avec plusieurs jours
     */
    public function test_calculate_duration_multiple_days(): void
    {
        $start = Carbon::parse('2024-01-01 23:00:00');
        $end = Carbon::parse('2024-01-02 07:00:00');

        $duration = $this->pointageService->calculateDuration($start, $end);

        $this->assertEquals('08:00:00', $duration);
    }

    /**
     * Test de vérification de capacité de pointage
     */
    public function test_can_user_point_at_site_within_range(): void
    {
        $latitude = 33.5732; // Très proche du site
        $longitude = -7.5899;

        $result = $this->pointageService->canUserPointAtSite(
            $this->user,
            $this->site,
            $latitude,
            $longitude
        );

        $this->assertTrue($result['can_point']);
        $this->assertArrayHasKey('distance_info', $result);
        $this->assertTrue($result['distance_info']['within_range']);
    }

    /**
     * Test de vérification de capacité de pointage (hors zone)
     */
    public function test_can_user_point_at_site_out_of_range(): void
    {
        $latitude = 34.0209; // Rabat (loin de Casablanca)
        $longitude = -6.8416;

        $result = $this->pointageService->canUserPointAtSite(
            $this->user,
            $this->site,
            $latitude,
            $longitude
        );

        $this->assertFalse($result['can_point']);
        $this->assertFalse($result['distance_info']['within_range']);
    }

    /**
     * Test des statistiques utilisateur
     */
    public function test_get_user_pointage_stats(): void
    {
        $from = Carbon::parse('2024-01-01');
        $to = Carbon::parse('2024-01-31');

        // Créer quelques pointages terminés
        Pointage::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'debut_pointage' => $from->copy()->addDays(1),
            'fin_pointage' => $from->copy()->addDays(1)->addHours(8),
            'duree' => '08:00:00',
        ]);

        $stats = $this->pointageService->getUserPointageStats($this->user->id, $from, $to);

        $this->assertIsArray($stats);
        $this->assertEquals(5, $stats['total_days']);
        $this->assertEquals(40, $stats['total_hours']); // 5 jours × 8 heures
        $this->assertEquals(8, $stats['average_hours_per_day']);
    }

    /**
     * Test de détection des pointages oubliés
     */
    public function test_check_forgotten_pointages(): void
    {
        // Créer un pointage oublié (commencé il y a 15 heures)
        Pointage::factory()->create([
            'user_id' => $this->user->id,
            'site_id' => $this->site->id,
            'debut_pointage' => Carbon::now()->subHours(15),
            'fin_pointage' => null,
        ]);

        // Créer un pointage récent (pas oublié)
        Pointage::factory()->create([
            'user_id' => $this->user->id,
            'site_id' => $this->site->id,
            'debut_pointage' => Carbon::now()->subHours(2),
            'fin_pointage' => null,
        ]);

        $forgotten = $this->pointageService->checkForgottenPointages();

        $this->assertCount(1, $forgotten);
        $this->assertGreaterThan(12, $forgotten[0]['hours_ago']);
    }

    /**
     * Test de mise en cache
     */
    public function test_caching_active_pointage(): void
    {
        // Vider le cache
        Cache::flush();

        $pointage = Pointage::factory()->create([
            'user_id' => $this->user->id,
            'fin_pointage' => null,
        ]);

        // Premier appel - devrait mettre en cache
        $result1 = $this->pointageService->getActivePointage($this->user->id);
        
        // Vérifier que c'est en cache
        $this->assertTrue(Cache::has("active_pointage:{$this->user->id}"));

        // Deuxième appel - devrait utiliser le cache
        $result2 = $this->pointageService->getActivePointage($this->user->id);

        $this->assertEquals($result1->id, $result2->id);
    }

    /**
     * Test de performance pour les statistiques
     */
    public function test_performance_user_stats(): void
    {
        // Créer beaucoup de pointages
        Pointage::factory()->count(100)->create([
            'user_id' => $this->user->id,
            'debut_pointage' => Carbon::now()->subDays(30),
            'fin_pointage' => Carbon::now()->subDays(30)->addHours(8),
            'duree' => '08:00:00',
        ]);

        $startTime = microtime(true);

        $stats = $this->pointageService->getUserPointageStats(
            $this->user->id,
            Carbon::now()->subDays(30),
            Carbon::now()
        );

        $endTime = microtime(true);
        $duration = $endTime - $startTime;

        // Le calcul des stats devrait être rapide même avec beaucoup de données
        $this->assertLessThan(0.5, $duration);
        $this->assertEquals(100, $stats['total_days']);
    }
}
