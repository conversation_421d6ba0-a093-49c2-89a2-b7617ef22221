<?php

// Test simple pour vérifier que PHP fonctionne
echo "PHP fonctionne correctement !<br>";
echo "Version PHP: " . phpversion() . "<br>";
echo "Date: " . date('Y-m-d H:i:s') . "<br>";

// Test de connexion à la base de données
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=clockin_db', 'root', '');
    echo "Connexion à la base de données: ✓ Réussie<br>";
    
    // Test de requête simple
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM sites");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Nombre de sites dans la base: " . $result['count'] . "<br>";
    
} catch (PDOException $e) {
    echo "Erreur de connexion à la base: " . $e->getMessage() . "<br>";
}

echo "<br><strong>Test terminé !</strong>";
