<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Index pour la table pointages
        Schema::table('pointages', function (Blueprint $table) {
            // Index composé pour les requêtes fréquentes
            $table->index(['user_id', 'debut_pointage'], 'idx_pointages_user_date');
            $table->index(['site_id', 'debut_pointage'], 'idx_pointages_site_date');
            
            // Index pour les pointages actifs (non terminés)
            $table->index(['user_id', 'fin_pointage'], 'idx_pointages_active');
            
            // Index pour les requêtes de plage de dates
            $table->index(['debut_pointage', 'fin_pointage'], 'idx_pointages_date_range');
            
            // Index pour les statistiques
            $table->index(['created_at'], 'idx_pointages_created');
        });

        // Index pour la table users
        Schema::table('users', function (Blueprint $table) {
            // Index pour les requêtes par rôle
            $table->index(['role'], 'idx_users_role');
            
            // Index pour les requêtes par email (si pas déjà unique)
            if (!$this->hasIndex('users', 'users_email_unique')) {
                $table->index(['email'], 'idx_users_email');
            }
            
            // Index pour les requêtes actives
            $table->index(['created_at'], 'idx_users_created');
        });

        // Index pour la table sites
        Schema::table('sites', function (Blueprint $table) {
            // Index spatial pour les coordonnées GPS (si supporté)
            if ($this->supportsSpatialIndexes()) {
                $table->spatialIndex(['latitude', 'longitude'], 'idx_sites_coordinates');
            } else {
                // Index séparés pour latitude et longitude
                $table->index(['latitude'], 'idx_sites_latitude');
                $table->index(['longitude'], 'idx_sites_longitude');
            }
            
            // Index pour les recherches par nom
            $table->index(['name'], 'idx_sites_name');
        });

        // Index pour la table assignments
        Schema::table('assignments', function (Blueprint $table) {
            // Index composé pour les requêtes d'assignation
            $table->index(['user_id', 'site_id'], 'idx_assignments_user_site');
            
            // Index pour les requêtes par site
            $table->index(['site_id'], 'idx_assignments_site');
            
            // Index pour les requêtes temporelles si la colonne existe
            if (Schema::hasColumn('assignments', 'created_at')) {
                $table->index(['created_at'], 'idx_assignments_created');
            }
        });

        // Index pour la table verifications
        if (Schema::hasTable('verifications')) {
            Schema::table('verifications', function (Blueprint $table) {
                // Index composé pour les requêtes fréquentes
                $table->index(['user_id', 'date_heure'], 'idx_verifications_user_date');
                $table->index(['site_id', 'date_heure'], 'idx_verifications_site_date');
                
                // Index pour les vérifications dans la zone
                $table->index(['is_within_range'], 'idx_verifications_within_range');
                
                // Index pour les vérifications automatiques
                $table->index(['is_automatic'], 'idx_verifications_automatic');
            });
        }

        // Index pour la table logs
        Schema::table('logs', function (Blueprint $table) {
            // Index composé pour les requêtes de logs
            $table->index(['user_id', 'created_at'], 'idx_logs_user_date');
            $table->index(['action', 'created_at'], 'idx_logs_action_date');
            
            // Index pour les requêtes par action
            $table->index(['action'], 'idx_logs_action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Supprimer les index de pointages
        Schema::table('pointages', function (Blueprint $table) {
            $table->dropIndex('idx_pointages_user_date');
            $table->dropIndex('idx_pointages_site_date');
            $table->dropIndex('idx_pointages_active');
            $table->dropIndex('idx_pointages_date_range');
            $table->dropIndex('idx_pointages_created');
        });

        // Supprimer les index de users
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_role');
            if ($this->hasIndex('users', 'idx_users_email')) {
                $table->dropIndex('idx_users_email');
            }
            $table->dropIndex('idx_users_created');
        });

        // Supprimer les index de sites
        Schema::table('sites', function (Blueprint $table) {
            if ($this->supportsSpatialIndexes()) {
                $table->dropSpatialIndex('idx_sites_coordinates');
            } else {
                $table->dropIndex('idx_sites_latitude');
                $table->dropIndex('idx_sites_longitude');
            }
            $table->dropIndex('idx_sites_name');
        });

        // Supprimer les index d'assignments
        Schema::table('assignments', function (Blueprint $table) {
            $table->dropIndex('idx_assignments_user_site');
            $table->dropIndex('idx_assignments_site');
            if (Schema::hasColumn('assignments', 'created_at')) {
                $table->dropIndex('idx_assignments_created');
            }
        });

        // Supprimer les index de verifications
        if (Schema::hasTable('verifications')) {
            Schema::table('verifications', function (Blueprint $table) {
                $table->dropIndex('idx_verifications_user_date');
                $table->dropIndex('idx_verifications_site_date');
                $table->dropIndex('idx_verifications_within_range');
                $table->dropIndex('idx_verifications_automatic');
            });
        }

        // Supprimer les index de logs
        Schema::table('logs', function (Blueprint $table) {
            $table->dropIndex('idx_logs_user_date');
            $table->dropIndex('idx_logs_action_date');
            $table->dropIndex('idx_logs_action');
        });
    }

    /**
     * Vérifie si un index existe
     */
    private function hasIndex(string $table, string $index): bool
    {
        $indexes = Schema::getConnection()->getDoctrineSchemaManager()
            ->listTableIndexes($table);
        
        return array_key_exists($index, $indexes);
    }

    /**
     * Vérifie si la base de données supporte les index spatiaux
     */
    private function supportsSpatialIndexes(): bool
    {
        $driver = Schema::getConnection()->getDriverName();
        return in_array($driver, ['mysql', 'pgsql']);
    }
};
