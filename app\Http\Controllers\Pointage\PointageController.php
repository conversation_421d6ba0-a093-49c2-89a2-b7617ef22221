<?php

namespace App\Http\Controllers\Pointage;

use App\Http\Controllers\Controller;
use App\Http\Requests\PointageRequest;
use App\Http\Resources\PointageResource;
use App\Models\Pointage;
use App\Models\Site;
use App\Models\User;
use App\Models\Log; // Assurez-vous que ce modèle existe et est correctement configuré
use App\Services\LocationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log as LaravelLog;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\PointagesExport;

class PointageController extends Controller
{
    private const MAX_DISTANCE = 50; // Distance maximale en mètres

    public function __construct(private LocationService $locationService)
    {
    }

    /**
     * Enregistre un pointage (début ou fin)
     * * @response 201 {
     * "success": true,
     * "message": "Début du pointage enregistré.",
     * "message_ar": "تم تسجيل بداية الحضور.",
     * "pointage": {
     * "id": 1,
     * "user_id": 2,
     * "site_id": 1,
     * "debut_pointage": "2023-01-01 08:00:00",
     * "fin_pointage": null,
     * "duree": null,
     * "debut_latitude": "33.57310000",
     * "debut_longitude": "-7.58980000"
     * }
     * }
     * @response 200 {
     * "success": true,
     * "message": "Fin du pointage enregistrée.",
     * "message_ar": "تم تسجيل نهاية الحضور.",
     * "pointage": {
     * "id": 1,
     * "user_id": 2,
     * "site_id": 1,
     * "debut_pointage": "2023-01-01 08:00:00",
     * "fin_pointage": "2023-01-01 16:30:00",
     * "duree": "08:30:00",
     * "debut_latitude": "33.57310000",
     * "debut_longitude": "-7.58980000",
     * "fin_latitude": "33.57310000",
     * "fin_longitude": "-7.58980000"
     * }
     * }
     * @response 403 {
     * "success": false,
     * "message": "Vous êtes hors du site assigné.",
     * "message_ar": "أنت خارج الموقع المخصص.",
     * "distance": 120.50
     * }
     */
    public function savePointage(PointageRequest $request): JsonResponse
    {
        LaravelLog::info('Requête de pointage reçue.', $request->all()); // Log de la requête entrante
        DB::beginTransaction();
        try {
            $user = $request->user();
            
            // Assurez-vous que l'utilisateur est authentifié
            if (!$user) {
                return $this->errorResponse('Utilisateur non authentifié.', 'المستخدم غير مصادق عليه.', 401);
            }

            $latitude = $request->latitude;
            $longitude = $request->longitude;

            // Vérifier l'assignation de l'utilisateur à un site
            $site = $this->getUserAssignedSite($user->id);
            if (!$site) {
                return $this->errorResponse('Aucun site assigné à cet utilisateur.', 'لا يوجد موقع مخصص لهذا المستخدم.', 403);
            }

            // Vérifier la distance
            $distance = $this->locationService->calculateDistance(
                (float) $site->latitude, // Assurez-vous que les types sont corrects
                (float) $site->longitude,
                (float) $latitude,
                (float) $longitude
            );

            if ($distance > self::MAX_DISTANCE) {
                DB::rollBack(); // Annuler la transaction en cas d'échec
                return response()->json([
                    'success' => false,
                    'message' => 'Vous êtes hors du site assigné. Distance: ' . round($distance, 2) . 'm',
                    'message_ar' => 'أنت خارج الموقع المخصص. المسافة: ' . round($distance, 2) . 'متر',
                    'distance' => round($distance, 2)
                ], 403);
            }

            // Gérer le pointage (début ou fin)
            return $this->handlePointage($user, $site, $latitude, $longitude);

        } catch (\Exception $e) {
            DB::rollBack();
            LaravelLog::error('Erreur lors de l\'enregistrement du pointage: ' . $e->getMessage(), ['exception' => $e]);
            return $this->errorResponse(
                'Erreur lors du pointage.',
                'حدث خطأ أثناء تسجيل التوقيت.',
                500,
                $e
            );
        }
    }

    /**
     * Liste des pointages (pour admin)
     * * @response 200 {
     * "success": true,
     * "data": [
     * {
     * "id": 1,
     * "user": {"id": 2, "name": "Ahmed Benali"},
     * "site": {"id": 1, "name": "Chantier Sidi Bernoussi"},
     * "debut_pointage": "2023-01-01 08:00:00",
     * "fin_pointage": "2023-01-01 16:30:00",
     * "duree": "08:30:00",
     * "debut_location": {"latitude": "33.57310000", "longitude": "-7.58980000"},
     * "fin_location": {"latitude": "33.57310000", "longitude": "-7.58980000"}
     * }
     * ],
     * "meta": {
     * "current_page": 1,
     * "total": 1,
     * "per_page": 15
     * }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            if (!$user || $user->role !== 'admin') {
                return $this->errorResponse('Accès non autorisé.', 'الوصول غير مصرح به.', 403);
            }

            $query = Pointage::with(['user', 'site'])
                ->orderBy('debut_pointage', 'desc');

            // Appliquer les filtres
            $this->applyFilters($query, $request);

            $pointages = $query->paginate($request->per_page ?? 15);

            // Journalisation
            // Assurez-vous que le modèle Log et la table 'logs' existent
            Log::create([
                'user_id' => $user->id,
                'action' => 'pointages_list',
                'details' => json_encode($request->all())
            ]);

            return response()->json([
                'success' => true,
                'data' => PointageResource::collection($pointages),
                'meta' => [
                    'current_page' => $pointages->currentPage(),
                    'total' => $pointages->total(),
                    'per_page' => $pointages->perPage(),
                    'last_page' => $pointages->lastPage()
                ]
            ]);

        } catch (\Exception $e) {
            LaravelLog::error('Erreur lors de la liste des pointages: ' . $e->getMessage(), ['exception' => $e]);
            return $this->errorResponse(
                'Erreur lors de la récupération des pointages.',
                'خطأ أثناء استرجاع سجلات الحضور.',
                500,
                $e
            );
        }
    }

    /**
     * Export Excel des pointages
     */
    public function export(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user || $user->role !== 'admin') {
                return $this->errorResponse('Accès non autorisé.', 'الوصول غير مصرح به.', 403);
            }

            $query = Pointage::with(['user', 'site'])
                ->orderBy('debut_pointage', 'desc');

            $this->applyFilters($query, $request);

            // Journalisation
            Log::create([
                'user_id' => $user->id,
                'action' => 'pointages_export',
                'details' => json_encode($request->all())
            ]);

            return Excel::download(
                new PointagesExport($query->get()),
                'pointages_' . now()->format('Y-m-d_H-i-s') . '.xlsx' // Ajout de l'heure pour un nom de fichier unique
            );

        } catch (\Exception $e) {
            LaravelLog::error('Erreur lors de l\'export des pointages: ' . $e->getMessage(), ['exception' => $e]);
            return $this->errorResponse(
                'Erreur lors de l\'export.',
                'خطأ أثناء التصدير.',
                500,
                $e
            );
        }
    }

    /**
     * Vérification de localisation
     * * @response 200 {
     * "success": true,
     * "within_range": true,
     * "distance": 0.00,
     * "site": "Chantier Sidi Bernoussi"
     * }
     */
    public function verifyLocation(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            if (!$user) {
                return $this->errorResponse('Utilisateur non authentifié.', 'المستخدم غير مصادق عليه.', 401);
            }

            $latitude = $request->latitude;
            $longitude = $request->longitude;

            // Validation de la présence de latitude et longitude
            if (is_null($latitude) || is_null($longitude)) {
                return $this->errorResponse('Latitude et longitude sont requises.', 'خطوط الطول والعرض مطلوبة.', 400);
            }

            $site = $this->getUserAssignedSite($user->id);
            if (!$site) {
                return $this->errorResponse('Aucun site assigné.', 'لا يوجد موقع مخصص.', 403);
            }

            $distance = $this->locationService->calculateDistance(
                (float) $site->latitude,
                (float) $site->longitude,
                (float) $latitude,
                (float) $longitude
            );

            $isWithinRange = $distance <= self::MAX_DISTANCE;

            // Enregistrer la vérification
            // Assurez-vous que la table 'verifications' existe et a les colonnes nécessaires
            DB::table('verifications')->insert([
                'user_id' => $user->id,
                'site_id' => $site->id,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'date_heure' => Carbon::now(),
                'is_within_range' => $isWithinRange,
                'distance' => $distance,
                'is_automatic' => true, // Supposons que cette vérification est automatique
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            return response()->json([
                'success' => true,
                'within_range' => $isWithinRange,
                'distance' => round($distance, 2),
                'site' => $site->name
            ]);

        } catch (\Exception $e) {
            LaravelLog::error('Erreur lors de la vérification de localisation: ' . $e->getMessage(), ['exception' => $e]);
            return $this->errorResponse(
                'Erreur lors de la vérification.',
                'خطأ أثناء التحقق.',
                500,
                $e
            );
        }
    }

    /**
     * Vérifie si l'utilisateur est dans la zone autorisée
     */
    public function checkLocation(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            if (!$user) {
                return $this->errorResponse('Utilisateur non authentifié.', 'المستخدم غير مصادق عليه.', 401);
            }

            $latitude = $request->latitude;
            $longitude = $request->longitude;

            // Validation de la présence de latitude et longitude
            if (is_null($latitude) || is_null($longitude)) {
                return $this->errorResponse('Latitude et longitude sont requises.', 'خطوط الطول والعرض مطلوبة.', 400);
            }

            $site = $this->getUserAssignedSite($user->id);
            if (!$site) {
                return $this->errorResponse('Aucun site assigné.', 'لا يوجد موقع مخصص.', 403);
            }

            $distance = $this->locationService->calculateDistance(
                (float) $site->latitude,
                (float) $site->longitude,
                (float) $latitude,
                (float) $longitude
            );

            $canPointe = $distance <= self::MAX_DISTANCE;

            return response()->json([
                'success' => true,
                'within_range' => $canPointe,
                'distance' => round($distance, 2),
                'site' => $site->name,
                'can_pointe' => $canPointe
            ]);

        } catch (\Exception $e) {
            LaravelLog::error('Erreur lors de la vérification de la localisation: ' . $e->getMessage(), ['exception' => $e]);
            return $this->errorResponse(
                'Erreur lors de la vérification.',
                'خطأ أثناء التحقق.',
                500,
                $e
            );
        }
    }

    // ============ Méthodes privées ============

    private function getUserAssignedSite(int $userId): ?Site
    {
        // Utiliser la relation Eloquent si disponible, sinon DB::table
        // Assurez-vous qu'il y a une table 'assignments' avec user_id et site_id
        $assignment = DB::table('assignments')
            ->where('user_id', $userId)
            ->first();

        // Retourne le modèle Site trouvé ou null
        return $assignment ? Site::find($assignment->site_id) : null;
    }

    private function handlePointage(User $user, Site $site, float $latitude, float $longitude): JsonResponse
    {
        // Chercher un pointage actif pour cet utilisateur (sans fin_pointage)
        $activePointage = Pointage::where('user_id', $user->id)
            ->whereNull('fin_pointage')
            ->first();

        if ($activePointage) {
            // Si un pointage actif existe, c'est une fin de pointage
            return $this->endPointage($activePointage, $latitude, $longitude);
        }

        // Sinon, c'est un début de pointage
        return $this->startPointage($user, $site, $latitude, $longitude);
    }

    private function startPointage(User $user, Site $site, float $latitude, float $longitude): JsonResponse
    {
        $pointage = Pointage::create([
            'user_id' => $user->id,
            'site_id' => $site->id,
            'debut_pointage' => Carbon::now(),
            'debut_latitude' => $latitude,
            'debut_longitude' => $longitude,
        ]);

        DB::commit(); // Valider la transaction ici

        return response()->json([
            'success' => true,
            'message' => 'Début du pointage enregistré.',
            'message_ar' => 'تم تسجيل بداية الحضور.',
            'pointage' => new PointageResource($pointage)
        ], 201);
    }

    private function endPointage(Pointage $pointage, float $latitude, float $longitude): JsonResponse
    {
        // Calculer la durée avant la mise à jour pour s'assurer que debut_pointage est bien défini
        $duration = $this->calculateDuration($pointage->debut_pointage, Carbon::now());

        $pointage->update([
            'fin_pointage' => Carbon::now(),
            'fin_latitude' => $latitude,
            'fin_longitude' => $longitude,
            'duree' => $duration
        ]);

        DB::commit(); // Valider la transaction ici

        return response()->json([
            'success' => true,
            'message' => 'Fin du pointage enregistrée.',
            'message_ar' => 'تم تسجيل نهاية الحضور.',
            'pointage' => new PointageResource($pointage)
        ]);
    }

    private function calculateDuration($start, $end): string
    {
        $start = Carbon::parse($start);
        $end = Carbon::parse($end);
        
        // Gérer le cas où la date de fin est antérieure à la date de début (ne devrait pas arriver en usage normal)
        if ($end->lessThan($start)) {
            return '00:00:00';
        }

        $diff = $end->diff($start);

        // Formater en HH:MM:SS
        // Total heures = jours * 24 + heures
        $totalHours = $diff->days * 24 + $diff->h;
        return sprintf('%02d:%02d:%02d', $totalHours, $diff->i, $diff->s);
    }

    private function applyFilters($query, Request $request): void
    {
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }
        if ($request->filled('site_id')) {
            $query->where('site_id', $request->site_id);
        }
        if ($request->filled('date_from')) {
            // whereDate pour comparer uniquement la date
            $query->whereDate('debut_pointage', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('debut_pointage', '<=', $request->date_to);
        }
        if ($request->filled('active') && filter_var($request->active, FILTER_VALIDATE_BOOLEAN)) {
            // Filtrer les pointages actifs (ceux qui n'ont pas de fin_pointage)
            $query->whereNull('fin_pointage');
        }
    }

    private function errorResponse(
        string $message, 
        string $messageAr, 
        int $code, 
        \Exception $e = null
    ): JsonResponse {
        return response()->json([
            'success' => false,
            'message' => $message,
            'message_ar' => $messageAr,
            'error' => config('app.debug') && $e ? $e->getMessage() : null, // Affiche le message d'erreur de l'exception en mode debug
            'trace' => config('app.debug') && $e ? $e->getTraceAsString() : null // Ajoute la trace en mode debug
        ], $code);
    }
}