<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
         executionOrder="depends,defects"
         failOnWarning="true"
         failOnRisky="true"
         failOnEmptyTestSuite="true"
         beStrictAboutOutputDuringTests="true"
         verbose="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
        <testsuite name="Performance">
            <directory>tests/Performance</directory>
        </testsuite>
    </testsuites>

    <source>
        <include>
            <directory>app</directory>
        </include>
        <exclude>
            <directory>app/Console/Commands</directory>
            <file>app/Http/Kernel.php</file>
        </exclude>
    </source>

    <coverage includeUncoveredFiles="true"
              processUncoveredFiles="true"
              ignoreDeprecatedCodeUnits="true"
              disableCodeCoverageIgnore="true">
        <report>
            <html outputDirectory="storage/app/test-coverage" lowUpperBound="50" highLowerBound="80"/>
            <text outputFile="php://stdout" showUncoveredFiles="false" showOnlySummary="true"/>
        </report>
    </coverage>

    <logging>
        <junit outputFile="storage/app/test-results.xml"/>
        <teamcity outputFile="storage/app/teamcity.txt"/>
    </logging>

    <php>
        <!-- Environment Configuration -->
        <env name="APP_ENV" value="testing"/>
        <env name="APP_KEY" value="base64:2fl+Ktvkdg+Fuz4Qp/A75G2RTiWVA/ZoKGFUJVaGd6o="/>
        <env name="APP_DEBUG" value="true"/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>

        <!-- Database Configuration -->
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>

        <!-- Cache Configuration -->
        <env name="CACHE_STORE" value="array"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>

        <!-- Security Configuration -->
        <env name="BCRYPT_ROUNDS" value="4"/>

        <!-- Mail Configuration -->
        <env name="MAIL_MAILER" value="array"/>

        <!-- Logging Configuration -->
        <env name="LOG_CHANNEL" value="single"/>
        <env name="LOG_LEVEL" value="debug"/>

        <!-- ClockIn Specific Configuration -->
        <env name="CLOCKIN_MAX_DISTANCE" value="50"/>
        <env name="CLOCKIN_FAKE_GPS" value="true"/>
        <env name="CLOCKIN_MOCK_SERVICES" value="true"/>

        <!-- Disable External Services -->
        <env name="PULSE_ENABLED" value="false"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
        <env name="SANCTUM_STATEFUL_DOMAINS" value="localhost"/>

        <!-- Performance Settings -->
        <ini name="memory_limit" value="512M"/>
        <ini name="max_execution_time" value="300"/>
    </php>
</phpunit>
