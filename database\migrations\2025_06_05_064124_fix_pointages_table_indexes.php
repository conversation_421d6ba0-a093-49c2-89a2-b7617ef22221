<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pointages', function (Blueprint $table) {
            // Supprimer les anciens index et créer les nouveaux avec les bons noms
            $table->dropIndex(['user_id']);
            $table->dropIndex(['site_id']);
            $table->dropIndex(['debut_pointage']);

            $table->index('user_id', 'idx_pointages_user');
            $table->index('site_id', 'idx_pointages_site');
            $table->index('debut_pointage', 'idx_pointages_debut');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pointages', function (Blueprint $table) {
            // Restaurer les anciens index
            $table->dropIndex('idx_pointages_user');
            $table->dropIndex('idx_pointages_site');
            $table->dropIndex('idx_pointages_debut');

            $table->index('user_id');
            $table->index('site_id');
            $table->index('debut_pointage');
        });
    }
};
