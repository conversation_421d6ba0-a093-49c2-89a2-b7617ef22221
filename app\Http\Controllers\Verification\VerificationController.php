<?php

namespace App\Http\Controllers\Verification;

use App\Http\Controllers\Controller;
use App\Http\Requests\Verification\RequestVerificationRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Models\Verification;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * @group Verification Management
 * 
 * APIs for managing verification requests
 */
class VerificationController extends Controller
{
    /**
     * Request verification for an employee
     * 
     * Creates a new verification request for an employee. Only employees can have verification requests.
     * 
     * @bodyParam user_id int required The ID of the employee to request verification for. Example: 2
     * 
     * @response 201 {
     *   "success": true,
     *   "message": "Verification request created successfully",
     *   "data": {
     *     "id": 1,
     *     "user_id": 2,
     *     "status": "pending",
     *     "requested_at": "2025-06-05T10:30:00.000000Z",
     *     "created_at": "2025-06-05T10:30:00.000000Z",
     *     "updated_at": "2025-06-05T10:30:00.000000Z",
     *     "user": {
     *       "id": 2,
     *       "name": "<PERSON>",
     *       "email": "<EMAIL>",
     *       "role": "employee"
     *     }
     *   }
     * }
     * 
     * @response 400 {
     *   "success": false,
     *   "message": "User not found"
     * }
     * 
     * @response 400 {
     *   "success": false,
     *   "message": "Only employees can request verification"
     * }
     * 
     * @response 409 {
     *   "success": false,
     *   "message": "A pending verification request already exists for this user"
     * }
     */
    public function requestVerification(RequestVerificationRequest $request): JsonResponse
    {
        try {
            Log::info('Verification request started', [
                'user_id' => $request->user_id,
                'requested_by' => auth()->id()
            ]);

            // Vérifier que l'utilisateur existe
            $user = User::find($request->user_id);
            if (!$user) {
                Log::warning('Verification request failed: User not found', [
                    'user_id' => $request->user_id
                ]);
                
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 400);
            }

            // Vérifier que l'utilisateur est un employé
            if (!$user->isEmployee()) {
                Log::warning('Verification request failed: User is not an employee', [
                    'user_id' => $request->user_id,
                    'user_role' => $user->role
                ]);
                
                return response()->json([
                    'success' => false,
                    'message' => 'Only employees can request verification'
                ], 400);
            }

            // Vérifier qu'il n'y a pas déjà une demande récente (dans les dernières 5 minutes)
            $recentRequest = Verification::where('user_id', $request->user_id)
                ->where('date_heure', '>=', now()->subMinutes(5))
                ->first();

            if ($recentRequest) {
                Log::warning('Verification request failed: Recent request already exists', [
                    'user_id' => $request->user_id,
                    'existing_request_id' => $recentRequest->id
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'A verification request was already sent recently for this user'
                ], 409);
            }

            Log::info('Verification request created successfully', [
                'user_id' => $request->user_id
            ]);

            // Dans une vraie application, ici on enverrait une notification push
            // ou un message à l'employé via Firebase, WebSocket, etc.

            return response()->json([
                'success' => true,
                'message' => 'Verification request sent successfully',
                'data' => [
                    'user' => new UserResource($user),
                    'requested_at' => now()->format('Y-m-d H:i:s')
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error creating verification request', [
                'user_id' => $request->user_id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the verification request'
            ], 500);
        }
    }
}
