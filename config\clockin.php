<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Configuration ClockIn
    |--------------------------------------------------------------------------
    |
    | Configuration spécifique à l'application ClockIn pour la gestion
    | du pointage des employés avec géolocalisation.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Distance maximale autorisée
    |--------------------------------------------------------------------------
    |
    | Distance maximale en mètres entre la position de l'employé et le site
    | pour autoriser le pointage.
    |
    */
    'max_distance' => env('CLOCKIN_MAX_DISTANCE', 50),

    /*
    |--------------------------------------------------------------------------
    | Durée d'expiration des tokens
    |--------------------------------------------------------------------------
    |
    | Durée en secondes avant expiration des tokens d'authentification.
    |
    */
    'token_expiry' => env('CLOCKIN_TOKEN_EXPIRY', 86400), // 24 heures

    /*
    |--------------------------------------------------------------------------
    | Configuration de cache
    |--------------------------------------------------------------------------
    |
    | Durées de cache pour différents types de données.
    |
    */
    'cache' => [
        'user_site_assignment' => env('CLOCKIN_CACHE_ASSIGNMENT', 1800), // 30 minutes
        'active_pointage' => env('CLOCKIN_CACHE_POINTAGE', 300), // 5 minutes
        'pointage_stats' => env('CLOCKIN_CACHE_STATS', 3600), // 1 heure
        'sites_list' => env('CLOCKIN_CACHE_SITES', 3600), // 1 heure
    ],

    /*
    |--------------------------------------------------------------------------
    | Limites de taux
    |--------------------------------------------------------------------------
    |
    | Limites de requêtes par minute pour différents types d'opérations.
    |
    */
    'rate_limits' => [
        'login' => env('CLOCKIN_RATE_LOGIN', 5),
        'pointage' => env('CLOCKIN_RATE_POINTAGE', 10),
        'api' => env('CLOCKIN_RATE_API', 60),
        'admin' => env('CLOCKIN_RATE_ADMIN', 120),
        'export' => env('CLOCKIN_RATE_EXPORT', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration de sauvegarde
    |--------------------------------------------------------------------------
    |
    | Paramètres pour la sauvegarde automatique des données.
    |
    */
    'backup' => [
        'enabled' => env('CLOCKIN_BACKUP_ENABLED', true),
        'frequency' => env('CLOCKIN_BACKUP_FREQUENCY', 'daily'),
        'retention_days' => env('CLOCKIN_BACKUP_RETENTION', 30),
        'disk' => env('CLOCKIN_BACKUP_DISK', 's3'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration de monitoring
    |--------------------------------------------------------------------------
    |
    | Paramètres pour le monitoring et les alertes.
    |
    */
    'monitoring' => [
        'enabled' => env('CLOCKIN_MONITORING_ENABLED', true),
        'slow_query_threshold' => env('CLOCKIN_SLOW_QUERY_MS', 1000),
        'memory_threshold' => env('CLOCKIN_MEMORY_THRESHOLD_MB', 128),
        'forgotten_pointage_hours' => env('CLOCKIN_FORGOTTEN_HOURS', 12),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration de sécurité
    |--------------------------------------------------------------------------
    |
    | Paramètres de sécurité avancés.
    |
    */
    'security' => [
        'max_login_attempts' => env('CLOCKIN_MAX_LOGIN_ATTEMPTS', 5),
        'lockout_duration' => env('CLOCKIN_LOCKOUT_DURATION', 15), // minutes
        'require_https' => env('CLOCKIN_REQUIRE_HTTPS', true),
        'log_failed_attempts' => env('CLOCKIN_LOG_FAILED_ATTEMPTS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration des notifications
    |--------------------------------------------------------------------------
    |
    | Paramètres pour les notifications push et email.
    |
    */
    'notifications' => [
        'enabled' => env('CLOCKIN_NOTIFICATIONS_ENABLED', true),
        'channels' => [
            'email' => env('CLOCKIN_EMAIL_NOTIFICATIONS', true),
            'push' => env('CLOCKIN_PUSH_NOTIFICATIONS', true),
            'slack' => env('CLOCKIN_SLACK_NOTIFICATIONS', false),
        ],
        'events' => [
            'pointage_start' => env('CLOCKIN_NOTIFY_START', false),
            'pointage_end' => env('CLOCKIN_NOTIFY_END', false),
            'forgotten_pointage' => env('CLOCKIN_NOTIFY_FORGOTTEN', true),
            'security_alert' => env('CLOCKIN_NOTIFY_SECURITY', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration des exports
    |--------------------------------------------------------------------------
    |
    | Paramètres pour les exports de données.
    |
    */
    'exports' => [
        'max_records' => env('CLOCKIN_EXPORT_MAX_RECORDS', 10000),
        'formats' => ['xlsx', 'csv', 'pdf'],
        'disk' => env('CLOCKIN_EXPORT_DISK', 'local'),
        'cleanup_after_hours' => env('CLOCKIN_EXPORT_CLEANUP_HOURS', 24),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration de géolocalisation
    |--------------------------------------------------------------------------
    |
    | Paramètres pour la géolocalisation et les calculs de distance.
    |
    */
    'geolocation' => [
        'precision' => env('CLOCKIN_GEO_PRECISION', 8), // décimales
        'cache_distance_calculations' => env('CLOCKIN_CACHE_DISTANCES', true),
        'validate_coordinates' => env('CLOCKIN_VALIDATE_COORDS', true),
        'log_location_checks' => env('CLOCKIN_LOG_LOCATION_CHECKS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration de performance
    |--------------------------------------------------------------------------
    |
    | Paramètres d'optimisation des performances.
    |
    */
    'performance' => [
        'enable_query_cache' => env('CLOCKIN_QUERY_CACHE', true),
        'pagination_default' => env('CLOCKIN_PAGINATION_DEFAULT', 15),
        'pagination_max' => env('CLOCKIN_PAGINATION_MAX', 100),
        'eager_load_relations' => env('CLOCKIN_EAGER_LOAD', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Configuration de développement
    |--------------------------------------------------------------------------
    |
    | Paramètres spécifiques au développement et au débogage.
    |
    */
    'development' => [
        'fake_gps_enabled' => env('CLOCKIN_FAKE_GPS', false),
        'debug_queries' => env('CLOCKIN_DEBUG_QUERIES', false),
        'mock_external_services' => env('CLOCKIN_MOCK_SERVICES', false),
    ],

];
