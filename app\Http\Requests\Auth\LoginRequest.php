<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'email.required' => 'L\'adresse email est requise. / عنوان البريد الإلكتروني مطلوب.',
            'email.email' => 'L\'adresse email doit être valide. / يجب أن يكون عنوان البريد الإلكتروني صالحًا.',
            'password.required' => 'Le mot de passe est requis. / كلمة المرور مطلوبة.',
            'password.min' => 'Le mot de passe doit contenir au moins 6 caractères. / يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل.',
        ];
    }
}
