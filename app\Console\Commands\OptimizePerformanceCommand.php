<?php

namespace App\Console\Commands;

use App\Services\CacheService;
use App\Services\PointageService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Commande pour optimiser les performances de l'application
 */
class OptimizePerformanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'clockin:optimize 
                            {--cache : Optimiser le cache}
                            {--db : Optimiser la base de données}
                            {--all : Optimiser tout}
                            {--preload : Précharger les données critiques}';

    /**
     * The console command description.
     */
    protected $description = 'Optimise les performances de l\'application ClockIn';

    public function __construct(
        private CacheService $cacheService,
        private PointageService $pointageService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Optimisation des performances ClockIn...');

        $options = $this->options();
        
        if ($options['all'] || $options['cache']) {
            $this->optimizeCache();
        }

        if ($options['all'] || $options['db']) {
            $this->optimizeDatabase();
        }

        if ($options['all'] || $options['preload']) {
            $this->preloadData();
        }

        if (!$options['cache'] && !$options['db'] && !$options['preload'] && !$options['all']) {
            $this->showHelp();
        }

        $this->info('✅ Optimisation terminée !');
        return 0;
    }

    /**
     * Optimise le cache
     */
    private function optimizeCache(): void
    {
        $this->info('📦 Optimisation du cache...');

        // Nettoyer le cache expiré
        $this->line('  - Nettoyage du cache expiré');
        $this->cacheService->cleanup();

        // Précharger les données critiques
        $this->line('  - Préchargement des données critiques');
        $this->cacheService->preloadCriticalData();

        // Optimiser la configuration du cache
        if (config('cache.default') === 'redis') {
            $this->optimizeRedisCache();
        }

        $this->info('✅ Cache optimisé');
    }

    /**
     * Optimise Redis spécifiquement
     */
    private function optimizeRedisCache(): void
    {
        $this->line('  - Optimisation Redis');
        
        try {
            // Obtenir des statistiques Redis
            $redis = Cache::getStore()->getRedis();
            $info = $redis->info('memory');
            
            $this->line("    Mémoire utilisée: " . ($info['used_memory_human'] ?? 'N/A'));
            $this->line("    Clés en cache: " . $redis->dbsize());
            
            // Optimiser la mémoire si nécessaire
            if (isset($info['used_memory']) && $info['used_memory'] > 100 * 1024 * 1024) { // 100MB
                $this->warn('    ⚠️  Utilisation mémoire élevée détectée');
                $redis->flushdb();
                $this->line('    Cache Redis vidé');
            }
            
        } catch (\Exception $e) {
            $this->error('    Erreur lors de l\'optimisation Redis: ' . $e->getMessage());
        }
    }

    /**
     * Optimise la base de données
     */
    private function optimizeDatabase(): void
    {
        $this->info('🗄️  Optimisation de la base de données...');

        // Analyser les tables
        $this->analyzeTables();

        // Optimiser les tables
        $this->optimizeTables();

        // Vérifier les index
        $this->checkIndexes();

        // Nettoyer les données anciennes
        $this->cleanupOldData();

        $this->info('✅ Base de données optimisée');
    }

    /**
     * Analyse les tables principales
     */
    private function analyzeTables(): void
    {
        $this->line('  - Analyse des tables');
        
        $tables = ['users', 'sites', 'pointages', 'assignments', 'logs'];
        
        foreach ($tables as $table) {
            try {
                if (DB::getDriverName() === 'mysql') {
                    DB::statement("ANALYZE TABLE {$table}");
                }
                $count = DB::table($table)->count();
                $this->line("    {$table}: {$count} enregistrements");
            } catch (\Exception $e) {
                $this->error("    Erreur pour {$table}: " . $e->getMessage());
            }
        }
    }

    /**
     * Optimise les tables
     */
    private function optimizeTables(): void
    {
        $this->line('  - Optimisation des tables');
        
        if (DB::getDriverName() === 'mysql') {
            $tables = ['pointages', 'logs', 'verifications'];
            
            foreach ($tables as $table) {
                try {
                    DB::statement("OPTIMIZE TABLE {$table}");
                    $this->line("    {$table} optimisée");
                } catch (\Exception $e) {
                    $this->error("    Erreur pour {$table}: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Vérifie les index
     */
    private function checkIndexes(): void
    {
        $this->line('  - Vérification des index');
        
        try {
            // Vérifier les requêtes lentes récentes
            $slowQueries = $this->getSlowQueries();
            
            if ($slowQueries > 0) {
                $this->warn("    ⚠️  {$slowQueries} requêtes lentes détectées");
                $this->line('    Considérez ajouter des index supplémentaires');
            } else {
                $this->line('    ✅ Aucune requête lente détectée');
            }
            
        } catch (\Exception $e) {
            $this->error('    Erreur lors de la vérification: ' . $e->getMessage());
        }
    }

    /**
     * Nettoie les données anciennes
     */
    private function cleanupOldData(): void
    {
        $this->line('  - Nettoyage des données anciennes');
        
        try {
            // Nettoyer les logs anciens (> 90 jours)
            $deletedLogs = DB::table('logs')
                ->where('created_at', '<', now()->subDays(90))
                ->delete();
            
            if ($deletedLogs > 0) {
                $this->line("    {$deletedLogs} logs anciens supprimés");
            }
            
            // Nettoyer les vérifications anciennes (> 30 jours)
            if (DB::getSchemaBuilder()->hasTable('verifications')) {
                $deletedVerifications = DB::table('verifications')
                    ->where('created_at', '<', now()->subDays(30))
                    ->delete();
                
                if ($deletedVerifications > 0) {
                    $this->line("    {$deletedVerifications} vérifications anciennes supprimées");
                }
            }
            
        } catch (\Exception $e) {
            $this->error('    Erreur lors du nettoyage: ' . $e->getMessage());
        }
    }

    /**
     * Précharge les données critiques
     */
    private function preloadData(): void
    {
        $this->info('⚡ Préchargement des données...');
        
        try {
            $this->cacheService->preloadCriticalData();
            $this->line('  - Données critiques préchargées');
            
            // Vérifier les pointages oubliés
            $forgotten = $this->pointageService->checkForgottenPointages();
            if (!empty($forgotten)) {
                $this->warn("  ⚠️  {count($forgotten)} pointages oubliés détectés");
            }
            
        } catch (\Exception $e) {
            $this->error('  Erreur lors du préchargement: ' . $e->getMessage());
        }
        
        $this->info('✅ Données préchargées');
    }

    /**
     * Obtient le nombre de requêtes lentes
     */
    private function getSlowQueries(): int
    {
        try {
            if (DB::getDriverName() === 'mysql') {
                $result = DB::select("SHOW GLOBAL STATUS LIKE 'Slow_queries'");
                return (int) ($result[0]->Value ?? 0);
            }
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération des requêtes lentes', [
                'error' => $e->getMessage()
            ]);
        }
        
        return 0;
    }

    /**
     * Affiche l'aide
     */
    private function showHelp(): void
    {
        $this->info('Options disponibles:');
        $this->line('  --cache    Optimiser le cache');
        $this->line('  --db       Optimiser la base de données');
        $this->line('  --preload  Précharger les données critiques');
        $this->line('  --all      Optimiser tout');
        $this->line('');
        $this->line('Exemples:');
        $this->line('  php artisan clockin:optimize --all');
        $this->line('  php artisan clockin:optimize --cache --preload');
    }
}
