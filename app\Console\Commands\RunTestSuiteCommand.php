<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Process;

/**
 * Commande pour exécuter la suite de tests avec métriques
 */
class RunTestSuiteCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'clockin:test 
                            {--coverage : Générer un rapport de couverture}
                            {--unit : Exécuter uniquement les tests unitaires}
                            {--feature : Exécuter uniquement les tests d\'intégration}
                            {--performance : Inclure les tests de performance}
                            {--parallel : Exécuter les tests en parallèle}';

    /**
     * The console command description.
     */
    protected $description = 'Exécute la suite de tests ClockIn avec métriques et rapports';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Exécution de la suite de tests ClockIn...');

        $startTime = microtime(true);
        $options = $this->options();

        // Préparer l'environnement de test
        $this->prepareTestEnvironment();

        // Construire la commande PHPUnit
        $command = $this->buildTestCommand($options);

        // Exécuter les tests
        $result = $this->executeTests($command);

        // Calculer les métriques
        $endTime = microtime(true);
        $duration = $endTime - $startTime;

        // Afficher le résumé
        $this->displaySummary($result, $duration, $options);

        return $result['exit_code'];
    }

    /**
     * Prépare l'environnement de test
     */
    private function prepareTestEnvironment(): void
    {
        $this->line('📋 Préparation de l\'environnement de test...');

        // Vérifier que la base de données de test est configurée
        if (config('database.default') !== 'sqlite') {
            $this->warn('⚠️  Il est recommandé d\'utiliser SQLite pour les tests');
        }

        // Nettoyer le cache
        $this->call('cache:clear');
        
        // Optimiser l'autoloader
        $this->call('optimize:clear');

        $this->line('✅ Environnement préparé');
    }

    /**
     * Construit la commande de test
     */
    private function buildTestCommand(array $options): array
    {
        $command = ['vendor/bin/phpunit'];

        // Options de base
        $command[] = '--testdox';
        $command[] = '--colors=always';

        // Filtres de tests
        if ($options['unit']) {
            $command[] = '--testsuite=Unit';
        } elseif ($options['feature']) {
            $command[] = '--testsuite=Feature';
        }

        // Couverture de code
        if ($options['coverage']) {
            $command[] = '--coverage-html=storage/app/test-coverage';
            $command[] = '--coverage-text';
            $command[] = '--coverage-clover=storage/app/coverage.xml';
        }

        // Tests en parallèle
        if ($options['parallel']) {
            $command[] = '--parallel';
        }

        // Configuration spécifique
        $command[] = '--configuration=phpunit.xml';

        return $command;
    }

    /**
     * Exécute les tests
     */
    private function executeTests(array $command): array
    {
        $this->line('🚀 Exécution des tests...');

        $process = Process::run(implode(' ', $command));

        $output = $process->output();
        $errorOutput = $process->errorOutput();

        // Afficher la sortie en temps réel
        if ($output) {
            $this->line($output);
        }

        if ($errorOutput) {
            $this->error($errorOutput);
        }

        return [
            'exit_code' => $process->exitCode(),
            'output' => $output,
            'error_output' => $errorOutput,
        ];
    }

    /**
     * Affiche le résumé des tests
     */
    private function displaySummary(array $result, float $duration, array $options): void
    {
        $this->newLine();
        $this->info('📊 Résumé de l\'exécution des tests');
        $this->line('═══════════════════════════════════════');

        // Statut général
        if ($result['exit_code'] === 0) {
            $this->info('✅ Tous les tests sont passés avec succès !');
        } else {
            $this->error('❌ Certains tests ont échoué');
        }

        // Métriques de performance
        $this->line("⏱️  Durée d'exécution: " . round($duration, 2) . " secondes");
        $this->line("💾 Mémoire utilisée: " . $this->formatBytes(memory_get_peak_usage(true)));

        // Analyser la sortie pour extraire les métriques
        $metrics = $this->parseTestMetrics($result['output']);
        
        if (!empty($metrics)) {
            $this->newLine();
            $this->line('📈 Métriques des tests:');
            
            if (isset($metrics['tests_run'])) {
                $this->line("   Tests exécutés: {$metrics['tests_run']}");
            }
            
            if (isset($metrics['assertions'])) {
                $this->line("   Assertions: {$metrics['assertions']}");
            }
            
            if (isset($metrics['failures'])) {
                $this->line("   Échecs: {$metrics['failures']}");
            }
            
            if (isset($metrics['errors'])) {
                $this->line("   Erreurs: {$metrics['errors']}");
            }
        }

        // Informations sur la couverture
        if ($options['coverage']) {
            $this->newLine();
            $this->info('📋 Rapport de couverture généré dans storage/app/test-coverage/');
        }

        // Recommandations
        $this->displayRecommendations($result, $duration, $options);
    }

    /**
     * Parse les métriques des tests depuis la sortie
     */
    private function parseTestMetrics(string $output): array
    {
        $metrics = [];

        // Extraire le nombre de tests
        if (preg_match('/Tests: (\d+)/', $output, $matches)) {
            $metrics['tests_run'] = (int) $matches[1];
        }

        // Extraire le nombre d'assertions
        if (preg_match('/Assertions: (\d+)/', $output, $matches)) {
            $metrics['assertions'] = (int) $matches[1];
        }

        // Extraire les échecs
        if (preg_match('/Failures: (\d+)/', $output, $matches)) {
            $metrics['failures'] = (int) $matches[1];
        }

        // Extraire les erreurs
        if (preg_match('/Errors: (\d+)/', $output, $matches)) {
            $metrics['errors'] = (int) $matches[1];
        }

        return $metrics;
    }

    /**
     * Affiche des recommandations basées sur les résultats
     */
    private function displayRecommendations(array $result, float $duration, array $options): void
    {
        $this->newLine();
        $this->line('💡 Recommandations:');

        // Performance
        if ($duration > 60) {
            $this->warn('   ⚠️  Les tests prennent plus d\'une minute. Considérez:');
            $this->line('      - Utiliser --parallel pour les tests en parallèle');
            $this->line('      - Optimiser les tests lents');
            $this->line('      - Utiliser des mocks pour les services externes');
        }

        // Couverture
        if (!$options['coverage']) {
            $this->line('   📊 Utilisez --coverage pour analyser la couverture de code');
        }

        // Tests spécifiques
        if (!$options['unit'] && !$options['feature']) {
            $this->line('   🎯 Utilisez --unit ou --feature pour des tests ciblés');
        }

        // Échecs
        if ($result['exit_code'] !== 0) {
            $this->error('   🔧 Corrigez les tests en échec avant de déployer');
            $this->line('   📝 Consultez les logs détaillés ci-dessus');
        }
    }

    /**
     * Formate les bytes en format lisible
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
