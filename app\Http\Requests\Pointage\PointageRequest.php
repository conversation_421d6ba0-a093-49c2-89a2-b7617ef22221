<?php

namespace App\Http\Requests\Pointage;

use Illuminate\Foundation\Http\FormRequest;

class PointageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'latitude' => [
                'required',
                'numeric',
                'between:-90,90',
                'regex:/^-?([1-8]?[0-9](\.[0-9]{1,8})?|90(\.0{1,8})?)$/',
            ],
            'longitude' => [
                'required',
                'numeric',
                'between:-180,180',
                'regex:/^-?((1[0-7][0-9]|[1-9]?[0-9])(\.[0-9]{1,8})?|180(\.0{1,8})?)$/',
            ],
            'accuracy' => [
                'sometimes',
                'numeric',
                'min:0',
                'max:100', // Précision requise pour le pointage
            ],
            'timestamp' => [
                'sometimes',
                'integer',
                'min:' . (now()->subMinutes(30)->timestamp), // Pas plus de 30 min dans le passé
                'max:' . (now()->addMinutes(2)->timestamp), // Pas plus de 2 min dans le futur
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'latitude.required' => 'La latitude est requise.',
            'latitude.numeric' => 'La latitude doit être un nombre valide.',
            'latitude.between' => 'La latitude doit être comprise entre -90 et 90 degrés.',
            'latitude.regex' => 'Format de latitude invalide.',

            'longitude.required' => 'La longitude est requise.',
            'longitude.numeric' => 'La longitude doit être un nombre valide.',
            'longitude.between' => 'La longitude doit être comprise entre -180 et 180 degrés.',
            'longitude.regex' => 'Format de longitude invalide.',

            'accuracy.numeric' => 'La précision doit être un nombre.',
            'accuracy.min' => 'La précision ne peut pas être négative.',
            'accuracy.max' => 'La précision GPS doit être inférieure à 100 mètres pour le pointage.',

            'timestamp.integer' => 'Le timestamp doit être un entier.',
            'timestamp.min' => 'Le timestamp ne peut pas être antérieur à 30 minutes.',
            'timestamp.max' => 'Le timestamp ne peut pas être dans le futur.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validatePointageConstraints($validator);
        });
    }

    /**
     * Valide les contraintes spécifiques au pointage
     */
    private function validatePointageConstraints($validator): void
    {
        $latitude = $this->input('latitude');
        $longitude = $this->input('longitude');
        $accuracy = $this->input('accuracy');

        // Vérifier que les coordonnées ne sont pas nulles
        if ($latitude == 0 && $longitude == 0) {
            $validator->errors()->add('coordinates',
                'Coordonnées GPS invalides. Veuillez activer votre localisation.'
            );
        }

        // Vérifier la précision GPS pour le pointage
        if ($accuracy !== null && $accuracy > 50) {
            $validator->errors()->add('accuracy',
                'Signal GPS insuffisant pour le pointage. Veuillez vous déplacer vers un endroit avec un meilleur signal.'
            );
        }

        // Validation anti-fraude basique
        $this->validateAntiFraud($validator, $latitude, $longitude);
    }

    /**
     * Validation anti-fraude
     */
    private function validateAntiFraud($validator, $latitude, $longitude): void
    {
        // Vérifier si l'utilisateur a déjà fait un pointage récent à une distance importante
        $user = $this->user();
        if ($user) {
            $lastPointage = $user->pointages()
                ->where('created_at', '>', now()->subMinutes(5))
                ->latest()
                ->first();

            if ($lastPointage &&
                ($lastPointage->debut_latitude || $lastPointage->fin_latitude)) {

                $lastLat = $lastPointage->fin_latitude ?? $lastPointage->debut_latitude;
                $lastLon = $lastPointage->fin_longitude ?? $lastPointage->debut_longitude;

                // Calculer la distance approximative
                $distance = $this->calculateDistance($lastLat, $lastLon, $latitude, $longitude);

                // Si la distance est > 1km en moins de 5 minutes, c'est suspect
                if ($distance > 1000) {
                    $validator->errors()->add('coordinates',
                        'Déplacement trop rapide détecté. Veuillez attendre quelques minutes.'
                    );
                }
            }
        }
    }

    /**
     * Calcul de distance simple (approximatif)
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 6371000; // Rayon de la Terre en mètres

        $latDelta = deg2rad($lat2 - $lat1);
        $lonDelta = deg2rad($lon2 - $lon1);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($lonDelta / 2) * sin($lonDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }
}