<?php

namespace App\Providers;

use App\Services\LocationService;
use App\Services\PointageService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Events\QueryExecuted;

/**
 * Service Provider pour les services spécifiques à ClockIn
 */
class ClockinServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Enregistrement du LocationService comme singleton
        $this->app->singleton(LocationService::class, function ($app) {
            return new LocationService();
        });

        // Enregistrement du PointageService
        $this->app->singleton(PointageService::class, function ($app) {
            return new PointageService($app->make(LocationService::class));
        });

        // Enregistrement d'autres services spécifiques
        $this->registerCacheServices();
        $this->registerPerformanceServices();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->configureQueryLogging();
        $this->configureCacheOptimizations();
        $this->configurePerformanceMonitoring();
    }

    /**
     * Enregistre les services de cache spécialisés
     */
    private function registerCacheServices(): void
    {
        // Cache pour les données de géolocalisation
        $this->app->singleton('cache.geolocation', function ($app) {
            return Cache::store('geolocation');
        });

        // Cache pour les pointages
        $this->app->singleton('cache.pointages', function ($app) {
            return Cache::store('pointages');
        });
    }

    /**
     * Enregistre les services de performance
     */
    private function registerPerformanceServices(): void
    {
        // Service de monitoring des performances
        $this->app->singleton('performance.monitor', function ($app) {
            return new class {
                public function startTimer(string $operation): float
                {
                    return microtime(true);
                }

                public function endTimer(float $start, string $operation): void
                {
                    $duration = microtime(true) - $start;
                    if ($duration > 1.0) { // Log si > 1 seconde
                        Log::warning("Opération lente détectée", [
                            'operation' => $operation,
                            'duration' => $duration,
                            'memory_usage' => memory_get_usage(true)
                        ]);
                    }
                }
            };
        });
    }

    /**
     * Configure le logging des requêtes pour le monitoring
     */
    private function configureQueryLogging(): void
    {
        if (config('app.debug') || config('app.env') === 'local') {
            DB::listen(function (QueryExecuted $query) {
                if ($query->time > 1000) { // Log si > 1 seconde
                    Log::warning('Requête lente détectée', [
                        'sql' => $query->sql,
                        'bindings' => $query->bindings,
                        'time' => $query->time . 'ms',
                        'connection' => $query->connectionName
                    ]);
                }
            });
        }
    }

    /**
     * Configure les optimisations de cache
     */
    private function configureCacheOptimizations(): void
    {
        // Configuration du cache pour les données fréquemment accédées
        if ($this->app->environment('production')) {
            // Préchargement des données critiques
            $this->preloadCriticalData();
        }
    }

    /**
     * Configure le monitoring des performances
     */
    private function configurePerformanceMonitoring(): void
    {
        // Monitoring de la mémoire
        if (memory_get_usage(true) > 128 * 1024 * 1024) { // 128MB
            Log::warning('Utilisation mémoire élevée', [
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true)
            ]);
        }
    }

    /**
     * Précharge les données critiques en cache
     */
    private function preloadCriticalData(): void
    {
        try {
            // Précharger les sites actifs
            Cache::remember('sites.active', 3600, function () {
                return DB::table('sites')->get();
            });

            // Précharger les assignations utilisateur-site
            Cache::remember('assignments.all', 1800, function () {
                return DB::table('assignments')
                    ->join('users', 'assignments.user_id', '=', 'users.id')
                    ->join('sites', 'assignments.site_id', '=', 'sites.id')
                    ->select('assignments.*', 'users.name as user_name', 'sites.name as site_name')
                    ->get();
            });

        } catch (\Exception $e) {
            Log::error('Erreur lors du préchargement des données', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Services fournis par ce provider
     */
    public function provides(): array
    {
        return [
            LocationService::class,
            'cache.geolocation',
            'cache.pointages',
            'performance.monitor'
        ];
    }
}
